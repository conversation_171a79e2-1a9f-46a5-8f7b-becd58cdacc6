package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
)

const (
	userCachePrefixByID    = "user:id:"
	userCachePrefixByEmail = "user:email:"
	userCacheTTL           = 10 * time.Minute
)

type userInfrastructure struct {
	db     *database.DB
	redis  *redis.Client
	logger *logging.Logger
}

type UserRepository interface {
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, userID string) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
}

func NewUserRepository(db *database.DB, redis *redis.Client, logger *logging.Logger) UserRepository {
	return &userInfrastructure{
		db:     db,
		redis:  redis,
		logger: logger,
	}
}

func (r *userInfrastructure) getUserFromCache(ctx context.Context, key string) (*model.User, error) {
	log := r.logger.WithContext(ctx)

	val, err := r.redis.Get(ctx, key)
	if err != nil || val == "" {
		return nil, err
	}

	log.Debugf("Cache hit for key: %s", key)
	var user model.User
	if err := json.Unmarshal([]byte(val), &user); err != nil {
		log.Errorf("Failed to unmarshal user from cache for key %s: %v", key, err)
		return nil, err
	}
	return &user, nil
}

func (r *userInfrastructure) setUserInCache(ctx context.Context, user *model.User) {
	log := r.logger.WithContext(ctx)

	userBytes, err := json.Marshal(user)
	if err != nil {
		log.Errorf("Failed to marshal user for caching (ID: %s): %v", user.ID, err)
		return
	}

	keyByID := fmt.Sprintf("%s%s", userCachePrefixByID, user.ID)
	if err := r.redis.Set(ctx, keyByID, userBytes, userCacheTTL); err != nil {
		log.Errorf("Failed to set user cache for key %s: %v", keyByID, err)
	}
}

func (r *userInfrastructure) invalidateUserCache(ctx context.Context, user *model.User) {
	keyByID := fmt.Sprintf("%s%s", userCachePrefixByID, user.ID)
	if err := r.redis.Del(ctx, keyByID); err != nil {
		r.logger.WithContext(ctx).Errorf("Failed to invalidate user cache for user %s: %v", user.ID, err)
	}
}

func (r *userInfrastructure) Create(ctx context.Context, user *model.User) error {
	if err := r.db.WithContext(ctx).Create(user).Error; err != nil {
		return err
	}
	r.invalidateUserCache(ctx, user)
	return nil
}

func (r *userInfrastructure) GetByID(ctx context.Context, userID string) (*model.User, error) {
	cacheKey := fmt.Sprintf("%s%s", userCachePrefixByID, userID)

	cachedUser, _ := r.getUserFromCache(ctx, cacheKey)
	if cachedUser != nil {
		return cachedUser, nil
	}

	var user model.User
	err := r.db.WithContext(ctx).Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}

	r.setUserInCache(ctx, &user)
	return &user, nil
}

func (r *userInfrastructure) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	r.setUserInCache(ctx, &user)
	return &user, nil
}
