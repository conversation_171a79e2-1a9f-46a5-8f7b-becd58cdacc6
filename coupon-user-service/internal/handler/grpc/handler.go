package handler

import (
	"context"

	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"

	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/service"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type UserServer struct {
	proto_user_v1.UnimplementedUserServiceServer
	svc service.UserService
}

func NewUserServer(svc service.UserService) *UserServer {
	return &UserServer{svc: svc}
}

func convertUserTypeToProto(userType model.UserType) proto_user_v1.UserType {
	switch userType {
	case model.UserTypeNew:
		return proto_user_v1.UserType_USER_TYPE_NEW
	case model.UserTypeVIP:
		return proto_user_v1.UserType_USER_TYPE_VIP
	default:
		return proto_user_v1.UserType_USER_TYPE_UNSPECIFIED
	}
}

func (s *UserServer) Login(ctx context.Context, req *proto_user_v1.LoginRequest) (*proto_user_v1.LoginResponse, error) {
	user, token, err := s.svc.Login(ctx, req.Email, req.Password)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	grpc.SetHeader(ctx, metadata.Pairs("access_token", token))

	role := string(user.Role)

	return &proto_user_v1.LoginResponse{
		User: &proto_user_v1.User{
			Id:        user.ID,
			Email:     user.Email,
			Name:      user.Name,
			Role:      role,
			Type:      convertUserTypeToProto(user.Type),
			CreatedAt: timestamppb.New(user.CreatedAt),
			Password:  user.Password,
		},
	}, nil
}

func (s *UserServer) GetUser(ctx context.Context, req *proto_user_v1.GetUserRequest) (*proto_user_v1.GetUserResponse, error) {
	user, err := s.svc.GetUser(ctx, req.UserId)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	role := string(user.Role)

	return &proto_user_v1.GetUserResponse{
		User: &proto_user_v1.User{
			Id:        user.ID,
			Email:     user.Email,
			Name:      user.Name,
			Role:      role,
			Type:      convertUserTypeToProto(user.Type),
			CreatedAt: timestamppb.New(user.CreatedAt),
			Password:  user.Password,
		},
	}, nil
}

func (s *UserServer) GetUserByEmail(ctx context.Context, req *proto_user_v1.GetUserByEmailRequest) (*proto_user_v1.GetUserByEmailResponse, error) {
	user, err := s.svc.GetUserByEmail(ctx, req.Email)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	role := string(user.Role)

	return &proto_user_v1.GetUserByEmailResponse{
		User: &proto_user_v1.User{
			Id:        user.ID,
			Email:     user.Email,
			Name:      user.Name,
			Role:      role,
			Type:      convertUserTypeToProto(user.Type),
			CreatedAt: timestamppb.New(user.CreatedAt),
			Password:  user.Password,
		},
	}, nil
}

func (s *UserServer) CreateUser(ctx context.Context, req *proto_user_v1.CreateUserRequest) (*proto_user_v1.CreateUserResponse, error) {
	user, err := s.svc.RegisterUser(ctx, req.Name, req.Email, req.Password)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	role := string(user.Role)

	return &proto_user_v1.CreateUserResponse{
		User: &proto_user_v1.User{
			Id:        user.ID,
			Email:     user.Email,
			Name:      user.Name,
			Role:      role,
			Type:      convertUserTypeToProto(user.Type),
			CreatedAt: timestamppb.New(user.CreatedAt),
			Password:  user.Password,
		},
	}, nil
}

func (s *UserServer) HealthCheck(ctx context.Context, req *proto_common_v1.HealthCheckRequest) (*proto_common_v1.HealthCheckResponse, error) {
	return &proto_common_v1.HealthCheckResponse{Status: proto_common_v1.HealthCheckResponse_SERVING_STATUS_SERVING}, nil
}
