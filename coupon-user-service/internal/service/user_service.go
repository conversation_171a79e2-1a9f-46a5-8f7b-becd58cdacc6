package service

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/repository"
)

type UserService interface {
	RegisterUser(ctx context.Context, name, email, password string) (*model.User, error)
	GetUser(ctx context.Context, userID string) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	Login(ctx context.Context, email, password string) (*model.User, string, error)
}

type userService struct {
	repo       repository.UserRepository
	logger     *logging.Logger
	jwtManager *auth.JWTManager
}

func NewUserService(repo repository.UserRepository, logger *logging.Logger, jwtManager *auth.JWTManager) UserService {
	return &userService{
		repo:       repo,
		logger:     logger,
		jwtManager: jwtManager,
	}
}

func (s *userService) RegisterUser(ctx context.Context, name, email, password string) (*model.User, error) {
	log := s.logger.WithContext(ctx)
	log.Infof("Attempting to register user with email: %s", email)

	_, err := s.repo.GetByEmail(ctx, email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, app_errors.NewInternalError(fmt.Sprintf("db error checking for existing user: %v", err))
	}
	if err == nil {
		return nil, app_errors.NewConflictError(fmt.Sprintf("user with email %s already exists", email))
	}

	user, err := model.NewUser(name, email, password)
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to prepare user: %v", err))
	}
	if err := s.repo.Create(ctx, user); err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to create user profile: %v", err))
	}
	log.Infof("User profile created for user_id: %s", user.ID)

	return user, nil
}

func (s *userService) GetUser(ctx context.Context, userID string) (*model.User, error) {
	user, err := s.repo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("user with id %s not found", userID))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user: %v", err))
	}
	return user, nil
}

func (s *userService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.repo.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("user with email %s not found", email))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user by email: %v", err))
	}
	return user, nil
}

func (s *userService) Login(ctx context.Context, email, password string) (*model.User, string, error) {
	user, err := s.repo.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, "", app_errors.NewUnauthorizedError("invalid credentials")
		}
		return nil, "", app_errors.NewInternalError(fmt.Sprintf("failed to get user: %v", err))
	}

	if !user.CheckPassword(password) {
		return nil, "", app_errors.NewUnauthorizedError("invalid credentials")
	}

	token, err := s.jwtManager.GenerateToken(user.ID, user.Email, string(user.Role))
	if err != nil {
		return nil, "", app_errors.NewInternalError("failed to generate token")
	}

	return user, token, nil
}
