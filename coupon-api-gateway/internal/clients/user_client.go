package clients

import (
	"context"
	"fmt"
	"time"

	user_proto_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type UserClient struct {
	Client user_proto_v1.UserServiceClient
	conn   *shared_grpc.Client
}

func NewUserClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*UserClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for user service: %w", err)
	}
	return &UserClient{
		Client: user_proto_v1.NewUserServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *UserClient) Close() {
	c.conn.Close()
}

func (c *UserClient) Login(ctx context.Context, email, password string) (*user_proto_v1.LoginResponse, error) {
	req := &user_proto_v1.LoginRequest{Email: email, Password: password}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.Client.Login(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *UserClient) CreateUser(ctx context.Context, name, email, password string) (*user_proto_v1.CreateUserResponse, error) {
	req := &user_proto_v1.CreateUserRequest{
		Name:     name,
		Email:    email,
		Password: password,
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.CreateUser(ctx, req)
}

func (c *UserClient) GetUser(ctx context.Context, userID string) (*user_proto_v1.GetUserResponse, error) {
	req := &user_proto_v1.GetUserRequest{UserId: userID}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetUser(ctx, req)
}

func (c *UserClient) GetUserByEmail(ctx context.Context, email string) (*user_proto_v1.GetUserByEmailResponse, error) {
	req := &user_proto_v1.GetUserByEmailRequest{Email: email}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return c.Client.GetUserByEmail(ctx, req)
}
