package handlers

import (
	"net/http"
	"strconv"

	echoAdapter "github.com/TickLabVN/tonic/adapters/echo"
	"github.com/TickLabVN/tonic/core/docs"
	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_product_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
)

type ProductHandler struct {
	productClient *clients.ProductClient
	logger        *logging.Logger
}

type productIDParams struct {
	ID string `param:"id" validate:"required"`
}

type categoryIDParams struct {
	ID string `param:"id" validate:"required"`
}

type ListProductsResponse struct {
	Products   []dto.ProductResponse  `json:"products"`
	Pagination dto.PaginationResponse `json:"pagination"`
}

type ListCategoriesResponse struct {
	Categories []dto.CategoryResponse `json:"categories"`
}

func NewProductHandler(p *clients.ProductClient, l *logging.Logger) *ProductHandler {
	return &ProductHandler{
		productClient: p,
		logger:        l,
	}
}

func (h *ProductHandler) RegisterPublicRoutes(g *echo.Group, spec *docs.OpenApi) {
	// Public product routes
	listProductsRoute := g.GET("/products", h.HandleListProducts)
	echoAdapter.AddRoute[struct {
		Page       int    `query:"page"`
		Limit      int    `query:"limit"`
		Search     string `query:"search"`
		CategoryID string `query:"category_id"`
		Status     string `query:"status"`
		SortBy     string `query:"sort_by"`
		SortOrder  string `query:"sort_order"`
	}, ListProductsResponse](spec, listProductsRoute, docs.OperationObject{Tags: []string{"Products API"}})

	getProductRoute := g.GET("/products/:id", h.HandleGetProduct)
	echoAdapter.AddRoute[productIDParams, dto.ProductResponse](spec, getProductRoute, docs.OperationObject{Tags: []string{"Products API"}})

	// Public category routes
	listCategoriesRoute := g.GET("/categories", h.HandleListCategories)
	echoAdapter.AddRoute[struct {
		Page   int    `query:"page"`
		Limit  int    `query:"limit"`
		Search string `query:"search"`
	}, ListCategoriesResponse](spec, listCategoriesRoute, docs.OperationObject{Tags: []string{"Categories API"}})
}

func (h *ProductHandler) RegisterProtectedRoutes(g *echo.Group, spec *docs.OpenApi) {
	// Admin product routes
	updateProductRoute := g.PUT("/products/:id", h.HandleUpdateProduct)
	echoAdapter.AddRoute[struct {
		productIDParams
		dto.UpdateProductRequest
	}, dto.ProductResponse](spec, updateProductRoute, docs.OperationObject{Tags: []string{"Products API"}})
}

// Product handlers

func (h *ProductHandler) HandleGetProduct(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("product ID is required"))
	}

	req := &proto_product_v1.GetProductRequest{
		Metadata:  &proto_common_v1.RequestMetadata{},
		ProductId: id,
	}

	res, err := h.productClient.GetProduct(c.Request().Context(), req)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	if res.Error != nil {
		return utils.HandleServiceError(c, res.Error)
	}

	productResponse := dto.ToProductResponse(res.Product)
	return c.JSON(http.StatusOK, productResponse)
}

func (h *ProductHandler) HandleUpdateProduct(c echo.Context) error {
	id := c.Param("id")
	if id == "" {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("product ID is required"))
	}

	var req dto.UpdateProductRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid request body"))
	}
	if err := c.Validate(req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse(err.Error()))
	}

	protoReq := &proto_product_v1.UpdateProductRequest{
		Metadata:      &proto_common_v1.RequestMetadata{},
		ProductId:     id,
		Name:          req.Name,
		Description:   req.Description,
		Price:         req.Price,
		CategoryId:    req.CategoryID,
		ImageUrl:      req.ImageURL,
		StockQuantity: req.StockQuantity,
		Status:        dto.ConvertStringToProductStatus(req.Status),
		Brand:         req.Brand,
		Sku:           req.SKU,
	}

	res, err := h.productClient.UpdateProduct(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	if res.Error != nil {
		return utils.HandleServiceError(c, res.Error)
	}

	productResponse := dto.ToProductResponse(res.Product)
	return c.JSON(http.StatusOK, productResponse)
}

func (h *ProductHandler) HandleListProducts(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	search := c.QueryParam("search")
	categoryID := c.QueryParam("category_id")
	status := c.QueryParam("status")
	sortBy := c.QueryParam("sort_by")
	sortOrder := c.QueryParam("sort_order")

	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 10
	}

	req := &proto_product_v1.ListProductsRequest{
		Metadata: &proto_common_v1.RequestMetadata{},
		Pagination: &proto_common_v1.PaginationRequest{
			Page:     int32(page),
			PageSize: int32(limit),
		},
		Search:     search,
		CategoryId: categoryID,
		Status:     dto.ConvertStringToProductStatus(status),
		SortBy:     sortBy,
		SortOrder:  sortOrder,
	}

	res, err := h.productClient.ListProducts(c.Request().Context(), req)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	if res.Error != nil {
		return utils.HandleServiceError(c, res.Error)
	}

	products := make([]dto.ProductResponse, len(res.Products))
	for i, product := range res.Products {
		products[i] = *dto.ToProductResponse(product)
	}

	response := ListProductsResponse{
		Products:   products,
		Pagination: dto.ToPaginationResponse(res.Pagination),
	}

	return c.JSON(http.StatusOK, response)
}

// Category handlers

func (h *ProductHandler) HandleListCategories(c echo.Context) error {
	page, _ := strconv.Atoi(c.QueryParam("page"))
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	search := c.QueryParam("search")

	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = 10
	}

	req := &proto_product_v1.ListCategoriesRequest{
		Metadata: &proto_common_v1.RequestMetadata{},
		Pagination: &proto_common_v1.PaginationRequest{
			Page:     int32(page),
			PageSize: int32(limit),
		},
		Search: search,
	}

	res, err := h.productClient.ListCategories(c.Request().Context(), req)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	if res.Error != nil {
		return utils.HandleServiceError(c, res.Error)
	}

	categories := make([]dto.CategoryResponse, len(res.Categories))
	for i, category := range res.Categories {
		categories[i] = *dto.ToCategoryResponse(category)
	}

	response := ListCategoriesResponse{
		Categories: categories,
	}

	return c.JSON(http.StatusOK, response)
}
