package dto

import (
	"time"

	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
)

type CreateVoucherRequest struct {
	VoucherCode       string    `json:"voucher_code" validate:"required,min=3,max=50"`
	Title             string    `json:"title" validate:"required,min=1,max=255"`
	Description       string    `json:"description"`
	DiscountTypeID    string    `json:"discount_type_id" validate:"required"`
	DiscountValue     float64   `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       string    `json:"usage_method" validate:"required,oneof=MANUAL AUTO"`
	ValidFrom         time.Time `json:"valid_from" validate:"required"`
	ValidUntil        time.Time `json:"valid_until" validate:"required"`
	MaxUsageCount     *int32    `json:"max_usage_count,omitempty"`
	MaxUsagePerUser   *int32    `json:"max_usage_per_user,omitempty"`
	MinOrderAmount    float64   `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64  `json:"max_discount_amount,omitempty"`
}

type UpdateVoucherRequest struct {
	Title             string    `json:"title" validate:"required,min=1,max=255"`
	Description       string    `json:"description"`
	DiscountTypeID    string    `json:"discount_type_id" validate:"required"`
	DiscountValue     float64   `json:"discount_value" validate:"required,gt=0"`
	UsageMethod       string    `json:"usage_method" validate:"required,oneof=MANUAL AUTO"`
	Status            string    `json:"status" validate:"required,oneof=ACTIVE INACTIVE EXPIRED"`
	MinOrderAmount    float64   `json:"min_order_amount" validate:"gte=0"`
	MaxDiscountAmount *float64  `json:"max_discount_amount,omitempty"`
	MaxUsageCount     *int32    `json:"max_usage_count,omitempty"`
	MaxUsagePerUser   *int32    `json:"max_usage_per_user,omitempty"`
	ValidFrom         time.Time `json:"valid_from" validate:"required"`
	ValidUntil        time.Time `json:"valid_until" validate:"required"`
}

type CheckVoucherEligibilityRequest struct {
	VoucherCode    string     `json:"voucher_code" validate:"required"`
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp *time.Time `json:"order_timestamp,omitempty"`
	CartItems      []CartItem `json:"cart_items,omitempty"`
}

type ListAutoEligibleVouchersRequest struct {
	OrderAmount    float64    `json:"order_amount" validate:"required,gt=0"`
	OrderTimestamp *time.Time `json:"order_timestamp,omitempty"`
	CartItems      []CartItem `json:"cart_items,omitempty"`
}

type ListVouchersRequest struct {
	Page           int    `json:"page" query:"page"`
	Limit          int    `json:"limit" query:"limit"`
	Search         string `json:"search" query:"search"`
	DiscountTypeID *int   `json:"discount_type_id" query:"discount_type_id"`
	UsageMethod    string `json:"usage_method" query:"usage_method"`
	Status         string `json:"status" query:"status"`
	SortBy         string `json:"sort_by" query:"sort_by"`
	SortOrder      string `json:"sort_order" query:"sort_order"`
}

type CartItem struct {
	ProductID  *string `json:"product_id,omitempty"`
	CategoryID *string `json:"category_id,omitempty"`
	Quantity   int32   `json:"quantity" validate:"required,gt=0"`
	Price      float64 `json:"price" validate:"required,gte=0"`
}

type VoucherResponse struct {
	ID                string                `json:"id"`
	VoucherCode       string                `json:"voucher_code"`
	Title             string                `json:"title"`
	Description       string                `json:"description"`
	DiscountTypeID    string                `json:"discount_type_id"`
	DiscountValue     float64               `json:"discount_value"`
	UsageMethod       string                `json:"usage_method"`
	ValidFrom         time.Time             `json:"valid_from"`
	ValidUntil        time.Time             `json:"valid_until"`
	MaxUsageCount     *int32                `json:"max_usage_count,omitempty"`
	MaxUsagePerUser   *int32                `json:"max_usage_per_user,omitempty"`
	CurrentUsageCount int32                 `json:"current_usage_count"`
	MinOrderAmount    float64               `json:"min_order_amount"`
	MaxDiscountAmount *float64              `json:"max_discount_amount,omitempty"`
	CreatedBy         string                `json:"created_by"`
	Status            string                `json:"status"`
	CreatedAt         time.Time             `json:"created_at"`
	UpdatedAt         time.Time             `json:"updated_at"`
	DiscountType      *DiscountTypeResponse `json:"discount_type,omitempty"`
	TotalSavings      float64               `json:"total_savings"`
	UniqueUsers       int32                 `json:"unique_users"`
}

type DiscountTypeResponse struct {
	ID          string    `json:"id"`
	TypeCode    string    `json:"type_code"`
	TypeName    string    `json:"type_name"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type VoucherEligibilityResponse struct {
	Eligible       bool    `json:"eligible"`
	Message        string  `json:"message"`
	VoucherID      *string `json:"voucher_id,omitempty"`
	DiscountAmount float64 `json:"discount_amount"`
}

type EligibleVoucherResponse struct {
	Eligible       bool             `json:"eligible"`
	Voucher        *VoucherResponse `json:"voucher,omitempty"`
	DiscountAmount float64          `json:"discount_amount"`
}

type ListVouchersResponse struct {
	Data       []*VoucherResponse `json:"data"`
	Total      int32              `json:"total"`
	Page       int32              `json:"page"`
	Limit      int32              `json:"limit"`
	TotalPages int32              `json:"total_pages"`
}

func ToVoucherResponse(voucher *proto_voucher_v1.Voucher) *VoucherResponse {
	if voucher == nil {
		return nil
	}

	response := &VoucherResponse{
		ID:                voucher.Id,
		VoucherCode:       voucher.VoucherCode,
		Title:             voucher.Title,
		Description:       voucher.Description,
		DiscountTypeID:    voucher.DiscountTypeId,
		DiscountValue:     voucher.DiscountValue,
		UsageMethod:       convertUsageMethodToString(voucher.UsageMethod),
		ValidFrom:         voucher.ValidFrom.AsTime(),
		ValidUntil:        voucher.ValidUntil.AsTime(),
		CurrentUsageCount: voucher.CurrentUsageCount,
		MinOrderAmount:    voucher.MinOrderAmount,
		CreatedBy:         voucher.CreatedBy,
		Status:            convertVoucherStatusToString(voucher.Status),
		CreatedAt:         voucher.CreatedAt.AsTime(),
		UpdatedAt:         voucher.UpdatedAt.AsTime(),
		TotalSavings:      voucher.TotalSavings,
		UniqueUsers:       voucher.UniqueUsers,
	}

	if voucher.MaxUsageCount != nil {
		response.MaxUsageCount = voucher.MaxUsageCount
	}

	if voucher.MaxUsagePerUser != nil {
		response.MaxUsagePerUser = voucher.MaxUsagePerUser
	}

	if voucher.MaxDiscountAmount != nil {
		response.MaxDiscountAmount = voucher.MaxDiscountAmount
	}

	if voucher.DiscountType != nil {
		response.DiscountType = ToDiscountTypeResponse(voucher.DiscountType)
	}

	return response
}

func ToDiscountTypeResponse(discountType *proto_voucher_v1.DiscountType) *DiscountTypeResponse {
	if discountType == nil {
		return nil
	}

	return &DiscountTypeResponse{
		ID:          discountType.Id,
		TypeCode:    discountType.TypeCode,
		TypeName:    discountType.TypeName,
		Description: discountType.Description,
		IsActive:    discountType.IsActive,
		CreatedAt:   discountType.CreatedAt.AsTime(),
		UpdatedAt:   discountType.UpdatedAt.AsTime(),
	}
}

func convertUsageMethodToString(method proto_voucher_v1.UsageMethod) string {
	switch method {
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL:
		return "MANUAL"
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO:
		return "AUTO"
	default:
		return "MANUAL"
	}
}

func convertVoucherStatusToString(status proto_voucher_v1.VoucherStatus) string {
	switch status {
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE:
		return "ACTIVE"
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE:
		return "INACTIVE"
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED:
		return "EXPIRED"
	default:
		return "ACTIVE"
	}
}

func convertStringToUsageMethod(method string) proto_voucher_v1.UsageMethod {
	switch method {
	case "MANUAL":
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	case "AUTO":
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO
	default:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	}
}

func convertStringToVoucherStatus(status string) proto_voucher_v1.VoucherStatus {
	switch status {
	case "ACTIVE":
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	case "INACTIVE":
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE
	case "EXPIRED":
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED
	default:
		return proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	}
}

func ConvertStringToUsageMethod(method string) proto_voucher_v1.UsageMethod {
	return convertStringToUsageMethod(method)
}

func ConvertStringToVoucherStatus(status string) proto_voucher_v1.VoucherStatus {
	return convertStringToVoucherStatus(status)
}
