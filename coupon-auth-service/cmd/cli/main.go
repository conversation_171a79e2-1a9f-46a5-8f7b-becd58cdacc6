package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
)

func main() {
	var (
		serviceName    = flag.String("service-name", "", "Name of the service to register (required)")
		serviceVersion = flag.String("service-version", "1.0.0", "Version of the service")
		description    = flag.String("description", "", "Description of the service")
		authAddr       = flag.String("auth-addr", "localhost:50052", "Address of the auth service")
	)
	flag.Parse()

	if *serviceName == "" {
		fmt.Fprintf(os.Stderr, "Error: service-name is required\n")
		flag.Usage()
		os.Exit(1)
	}

	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	conn, err := grpc.NewClient(*authAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect to auth service at %s: %v", *authAddr, err)
	}
	defer conn.Close()

	client := proto_auth_v1.NewAuthServiceClient(conn)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	ctx = metadata.AppendToOutgoingContext(ctx, "bootstrap-token", cfg.Auth.BootstrapToken)

	req := &proto_auth_v1.RegisterServiceRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: fmt.Sprintf("cli-register-%d", time.Now().Unix()),
			Timestamp: timestamppb.Now(),
		},
		ServiceName:    *serviceName,
		ServiceVersion: *serviceVersion,
		Description:    *description,
	}

	fmt.Printf("Registering service: %s (version: %s)\n", *serviceName, *serviceVersion)
	fmt.Printf("Auth service address: %s\n", *authAddr)

	resp, err := client.RegisterService(ctx, req)
	if err != nil {
		log.Fatalf("Failed to register service: %v", err)
	}

	if resp.Error != nil {
		log.Fatalf("Service registration failed: %s", resp.Error.Message)
	}

	fmt.Printf("\n✅ Service registration successful!\n")
	fmt.Printf("Service ID: %s\n", resp.ServiceId)
	fmt.Printf("Client ID: %s\n", resp.ClientId)
	fmt.Printf("Client Key: %s\n", resp.ClientKey)

	fmt.Printf("\n# Environment variables for CI/CD:\n")
	fmt.Printf("export %s_CLIENT_ID=%s\n", envVarName(*serviceName), resp.ClientId)
	fmt.Printf("export %s_CLIENT_KEY=%s\n", envVarName(*serviceName), resp.ClientKey)

	filename := fmt.Sprintf("%s-credentials.env", *serviceName)
	file, err := os.Create(filename)
	if err != nil {
		log.Printf("Warning: Could not create credentials file %s: %v", filename, err)
	} else {
		defer file.Close()
		fmt.Fprintf(file, "# Generated service credentials for %s\n", *serviceName)
		fmt.Fprintf(file, "# Generated at: %s\n", time.Now().Format(time.RFC3339))
		fmt.Fprintf(file, "%s_CLIENT_ID=%s\n", envVarName(*serviceName), resp.ClientId)
		fmt.Fprintf(file, "%s_CLIENT_KEY=%s\n", envVarName(*serviceName), resp.ClientKey)
		fmt.Printf("\n📄 Credentials saved to: %s\n", filename)
	}
}

func envVarName(serviceName string) string {
	result := ""
	for _, char := range serviceName {
		if char == '-' {
			result += "_"
		} else if char >= 'a' && char <= 'z' {
			result += string(char - 'a' + 'A')
		} else {
			result += string(char)
		}
	}
	return result
}
