package middleware

import (
	"context"
	"path"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var publicMethods = map[string]bool{
	"HealthCheck":                true,
	"ValidateServiceCredentials": true,
}

// CreateServiceAuthFunc creates a proper gRPC auth function for auth service
func CreateServiceAuthFunc(repo repository.AuthRepository, cfg *config.Config) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		ts := grpc.ServerTransportStreamFromContext(ctx)
		method := ""
		if ts != nil {
			method = path.Base(ts.Method())
		}

		// Allow public methods without authentication
		if publicMethods[method] {
			return ctx, nil
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Error(codes.Unauthenticated, "missing metadata")
		}

		// Handle RegisterService with bootstrap token
		if method == "RegisterService" {
			tokens := md.Get("bootstrap-token")
			if len(tokens) > 0 && tokens[0] == cfg.Auth.BootstrapToken {
				return ctx, nil
			}
			return nil, status.Error(codes.Unauthenticated, "invalid bootstrap token")
		}

		// Handle service-to-service authentication for other methods
		clientIDs := md.Get("client-id")
		clientKeys := md.Get("client-key")
		if len(clientIDs) == 0 || len(clientKeys) == 0 {
			return nil, status.Error(codes.Unauthenticated, "missing client credentials")
		}

		// Validate service credentials
		cred, err := repo.GetServiceByClientID(ctx, clientIDs[0])
		if err != nil {
			return nil, status.Error(codes.Unauthenticated, "invalid client credentials")
		}

		if bcrypt.CompareHashAndPassword([]byte(cred.ClientKey), []byte(clientKeys[0])) != nil {
			return nil, status.Error(codes.Unauthenticated, "invalid client credentials")
		}

		return ctx, nil
	}
}
