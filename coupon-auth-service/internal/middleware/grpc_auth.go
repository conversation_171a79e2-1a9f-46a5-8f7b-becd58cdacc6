package middleware

import (
	"context"
	"path"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var publicMethods = map[string]bool{
	"HealthCheck": true,
}

func CreateServiceAuthFunc(repo repository.AuthRepository, cfg *config.Config) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		ts := grpc.ServerTransportStreamFromContext(ctx)
		method := ""
		if ts != nil {
			method = path.Base(ts.Method())
		}
		if publicMethods[method] {
			return ctx, nil
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Error(codes.Unauthenticated, "missing metadata")
		}

		if method == "RegisterService" {
			tokens := md.Get("bootstrap-token")
			if len(tokens) > 0 && tokens[0] == cfg.Auth.BootstrapToken {
				return ctx, nil
			}
		}

		return ctx, nil
	}
}
