package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/redis/go-redis"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/clients"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-product-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/service"
	proto_product_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
	"google.golang.org/grpc"
)

func main() {
	cfg, err := config.Load("config/config.yaml")
	if err != nil {
		panic(fmt.Sprintf("Failed to load config: %v", err))
	}

	logger := logging.NewLogger(&cfg.Logging)
	logger.Info("Starting Product Service...")

	tracer, err := tracing.NewJaegerTracer(&cfg.Jaeger, cfg.Service.Name)
	if err != nil {
		logger.Fatalf("Failed to initialize tracer: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)

	db, err := database.NewConnection(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("Failed to connect to database: %v", err)
	}

	autoMigrator := database.NewAutoMigrator(db, logger)
	if err := autoMigrator.AutoMigrate(model.GetAllModels()...); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}

	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)
	jwtManager := auth.NewJWTManager(&cfg.Auth)

	// Create auth client for service credential validation
	authClient, err := clients.NewAuthClient(
		cfg.DownstreamServices.AuthServiceAddr,
		&cfg.GRPC,
		logger,
		appMetrics,
		cfg.Service.ClientID,
		cfg.Service.ClientKey,
	)
	if err != nil {
		logger.Fatalf("Failed to create auth client: %v", err)
	}
	defer authClient.Close()

	repo := repository.NewProductRepository(db, redisClient, logger)
	svc := service.NewProductService(repo, logger)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		authFunc := middleware.CreateServiceAuthFunc(jwtManager, authClient, cfg.Service.ClientID)
		startGRPCServer(ctx, cfg, logger, appMetrics, svc, authFunc)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, logger, healthChecker, appMetrics)
	}()

	wg.Wait()
	tracer.Close()
	db.Close()
	redisClient.Close()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, appMetrics *metrics.Metrics, svc service.ProductService, authFunc grpc.UnaryServerInterceptor) {
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", cfg.Service.GRPCPort))
	if err != nil {
		logger.Fatalf("Failed to listen on gRPC port %d: %v", cfg.Service.GRPCPort, err)
	}

	grpcServer := shared_grpc.NewServer(&cfg.GRPC, logger, appMetrics, authFunc)

	productServer := grpc_handler.NewProductServer(svc)
	proto_product_v1.RegisterProductServiceServer(grpcServer, productServer)

	logger.Infof("gRPC server listening on port %d", cfg.Service.GRPCPort)

	go func() {
		if err := grpcServer.Serve(lis); err != nil {
			logger.Errorf("gRPC server error: %v", err)
		}
	}()

	<-ctx.Done()
	logger.Info("Shutting down gRPC server...")

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	done := make(chan struct{})
	go func() {
		grpcServer.GracefulStop()
		close(done)
	}()

	select {
	case <-done:
		logger.Info("gRPC server stopped gracefully")
	case <-shutdownCtx.Done():
		logger.Warn("gRPC server shutdown timeout, forcing stop")
		grpcServer.Stop()
	}
}

func startHTTPServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, healthChecker *health.HealthChecker, appMetrics *metrics.Metrics) {
	e := echo.New()
	e.HideBanner = true

	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(appMetrics.Handler()))

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Service.Port),
		Handler: e,
	}

	logger.Infof("HTTP server listening on port %d", cfg.Service.Port)

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Errorf("HTTP server error: %v", err)
		}
	}()

	<-ctx.Done()
	logger.Info("Shutting down HTTP server...")

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Errorf("HTTP server shutdown error: %v", err)
	} else {
		logger.Info("HTTP server stopped gracefully")
	}
}
