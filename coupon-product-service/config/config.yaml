service:
  name: "product-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "${PRODUCT_SERVICE_CLIENT_ID}"
  client_key: "${PRODUCT_SERVICE_CLIENT_KEY}"

database:
  host: "postgres-product"
  port: 5432
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  name: "${POSTGRES_DB}"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  max_lifetime: "1h"

redis:
  host: "redis-product"
  port: 6379
  password: "${REDIS_PASSWORD}"
  db: 0
  pool_size: 10
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

auth:
  jwt_secret: "${JWT_SECRET_KEY}"
  jwt_expiration: "1h"

grpc:
  max_recv_msg_size: 4194304
  max_send_msg_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  keepalive_enforcement_min_time: "10s"
  keepalive_enforcement_permit_without_stream: true

jaeger:
  host: "jaeger"
  port: 6831

logging:
  level: "debug"
  format: "json"

metrics:
  port: 2112
  path: "/metrics"
