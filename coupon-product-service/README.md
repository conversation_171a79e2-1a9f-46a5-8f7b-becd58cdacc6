# Coupon Product Service

A microservice for managing products and categories in the coupon management system.

## Features

- **Product Management**: Get product details, update products, list products with filtering and pagination
- **Category Management**: List categories with search and pagination
- **gRPC API**: Internal service-to-service communication
- **HTTP Health Checks**: Health monitoring endpoints
- **Database Integration**: PostgreSQL with GORM auto-migration
- **Redis Caching**: Product and category caching for improved performance
- **Service Authentication**: Client-ID/Client-Key based authentication
- **Observability**: Jaeger tracing, Prometheus metrics, structured logging

## API Endpoints

### Products
- `GET /api/products` - List products (public)
- `GET /api/products/:id` - Get product by ID (public)
- `PUT /api/products/:id` - Update product (protected)

### Categories
- `GET /api/categories` - List categories (public)

## gRPC Services

- `GetProduct` - Retrieve product by ID
- `UpdateProduct` - Update product details
- `ListProducts` - List products with filtering
- `ListCategories` - List categories
- `HealthCheck` - Service health status

## Database Schema

### Products Table
- `id` (UUID, Primary Key)
- `name` (VARCHAR, NOT NULL)
- `description` (TEXT)
- `price` (DOUBLE, NOT NULL)
- `category_id` (UUID, Foreign Key)
- `image_url` (TEXT)
- `stock_quantity` (BIGINT, NOT NULL, DEFAULT 0)
- `status` (VARCHAR, NOT NULL, DEFAULT 'ACTIVE')
- `brand` (VARCHAR)
- `sku` (VARCHAR, UNIQUE)
- `created_at` (TIMESTAMP, NOT NULL)
- `updated_at` (TIMESTAMP, NOT NULL)

### Categories Table
- `id` (UUID, Primary Key)
- `name` (VARCHAR, NOT NULL, UNIQUE)
- `description` (TEXT)
- `created_at` (TIMESTAMP, NOT NULL)

## Configuration

The service uses environment variables for configuration:

```bash
# Service Configuration
PRODUCT_SERVICE_CLIENT_ID=<client-id>
PRODUCT_SERVICE_CLIENT_KEY=<client-key>

# Database Configuration
POSTGRES_USER=<username>
POSTGRES_PASSWORD=<password>
POSTGRES_DB=<database-name>

# Redis Configuration
REDIS_PASSWORD=<redis-password>

# JWT Configuration
JWT_SECRET_KEY=<secret-key>

# Build Configuration
GITLAB_USER=<gitlab-username>
GITLAB_TOKEN=<gitlab-token>
```

## Development

### Prerequisites
- Go 1.24+
- PostgreSQL
- Redis
- Docker (optional)

### Building
```bash
make build
```

### Running
```bash
make run
```

### Testing
```bash
make test
```

### Docker
```bash
make docker-build
```

## Service Registration

The product service needs to be registered with the auth service to obtain client credentials:

```bash
# From the auth-service directory
make register-service SERVICE_NAME=product-service SERVICE_VERSION=1.0.0 DESCRIPTION="Product management service"
```

This will generate the `PRODUCT_SERVICE_CLIENT_ID` and `PRODUCT_SERVICE_CLIENT_KEY` values.

## Integration

The product service integrates with:
- **API Gateway**: HTTP endpoints for external access
- **Auth Service**: Service authentication and authorization
- **Other Services**: Can be called by voucher-service, order-service, etc.

## Architecture

The service follows the established microservice patterns:
- **Handler Layer**: gRPC and HTTP request handling
- **Service Layer**: Business logic and validation
- **Repository Layer**: Data access and persistence
- **Model Layer**: Data structures and DTOs

## Caching Strategy

The service implements Redis caching for improved performance:

### Product Caching
- **Cache Key**: `product:id:{product_id}`
- **TTL**: 10 minutes
- **Operations**: Cached on read, invalidated on update

### Category Caching
- **Individual Categories**: `category:id:{category_id}` (TTL: 30 minutes)
- **Category Lists**: `categories:list` (TTL: 30 minutes, simple queries only)

### Cache Invalidation
- Product cache is invalidated when products are updated
- Category cache uses TTL-based expiration

## Monitoring

- **Health Check**: `GET /health` (includes database and Redis health)
- **Metrics**: `GET /metrics` (Prometheus format)
- **Tracing**: Jaeger integration
- **Logging**: Structured JSON logging
