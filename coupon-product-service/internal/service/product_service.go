package service

import (
	"context"
	"fmt"
	"strings"

	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-product-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gorm.io/gorm"
)

type ProductService interface {
	// Product operations
	GetProductByID(ctx context.Context, id string) (*model.Product, error)
	UpdateProduct(ctx context.Context, id string, req *model.UpdateProductRequest) (*model.Product, error)
	ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error)

	// Category operations
	ListCategories(ctx context.Context, req *model.ListCategoriesRequest) ([]*model.Category, int64, error)
}

type productService struct {
	repo   repository.ProductRepository
	logger *logging.Logger
}

func NewProductService(repo repository.ProductRepository, logger *logging.Logger) ProductService {
	return &productService{
		repo:   repo,
		logger: logger,
	}
}

// Product operations

func (s *productService) GetProductByID(ctx context.Context, id string) (*model.Product, error) {
	if id == "" {
		return nil, errors.NewValidationError("product ID is required", nil)
	}

	product, err := s.repo.GetProductByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("product not found", map[string]interface{}{
				"product_id": id,
			})
		}
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	return product, nil
}

func (s *productService) UpdateProduct(ctx context.Context, id string, req *model.UpdateProductRequest) (*model.Product, error) {
	if id == "" {
		return nil, errors.NewValidationError("product ID is required", nil)
	}

	if err := s.validateUpdateProductRequest(req); err != nil {
		return nil, err
	}

	// Get existing product
	product, err := s.repo.GetProductByID(ctx, id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("product not found", map[string]interface{}{
				"product_id": id,
			})
		}
		return nil, fmt.Errorf("failed to get product: %w", err)
	}

	// Validate category exists if provided
	if req.CategoryID != nil && *req.CategoryID != "" {
		_, err := s.repo.GetCategoryByID(ctx, *req.CategoryID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil, errors.NewValidationError("category not found", map[string]interface{}{
					"category_id": *req.CategoryID,
				})
			}
			return nil, fmt.Errorf("failed to validate category: %w", err)
		}
	}

	// Update product fields
	product.Name = req.Name
	product.Description = req.Description
	product.Price = req.Price
	product.CategoryID = req.CategoryID
	product.ImageURL = req.ImageURL
	product.StockQuantity = req.StockQuantity
	product.Status = req.Status
	product.Brand = req.Brand
	product.SKU = req.SKU

	if err := s.repo.UpdateProduct(ctx, product); err != nil {
		s.logger.WithContext(ctx).Errorf("Failed to update product: %v", err)
		return nil, fmt.Errorf("failed to update product: %w", err)
	}

	s.logger.WithContext(ctx).Infof("Product updated successfully: %s", product.ID)
	return product, nil
}

func (s *productService) ListProducts(ctx context.Context, req *model.ListProductsRequest) ([]*model.Product, int64, error) {
	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	products, total, err := s.repo.ListProducts(ctx, req)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("Failed to list products: %v", err)
		return nil, 0, fmt.Errorf("failed to list products: %w", err)
	}

	return products, total, nil
}

// Category operations

func (s *productService) ListCategories(ctx context.Context, req *model.ListCategoriesRequest) ([]*model.Category, int64, error) {
	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	categories, total, err := s.repo.ListCategories(ctx, req)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("Failed to list categories: %v", err)
		return nil, 0, fmt.Errorf("failed to list categories: %w", err)
	}

	return categories, total, nil
}

// Validation methods
func (s *productService) validateCreateProductRequest(req *model.CreateProductRequest) error {
	if strings.TrimSpace(req.Name) == "" {
		return errors.NewValidationError("product name is required", map[string]interface{}{
			"field": "name",
		})
	}

	if req.Price <= 0 {
		return errors.NewValidationError("product price must be greater than 0", map[string]interface{}{
			"field": "price",
			"value": req.Price,
		})
	}

	if req.StockQuantity < 0 {
		return errors.NewValidationError("stock quantity cannot be negative", map[string]interface{}{
			"field": "stock_quantity",
			"value": req.StockQuantity,
		})
	}

	if req.Status != "" && req.Status != model.ProductStatusActive && req.Status != model.ProductStatusInactive {
		return errors.NewValidationError("invalid product status", map[string]interface{}{
			"field": "status",
			"value": req.Status,
		})
	}

	return nil
}

func (s *productService) validateUpdateProductRequest(req *model.UpdateProductRequest) error {
	if strings.TrimSpace(req.Name) == "" {
		return errors.NewValidationError("product name is required", map[string]interface{}{
			"field": "name",
		})
	}

	if req.Price <= 0 {
		return errors.NewValidationError("product price must be greater than 0", map[string]interface{}{
			"field": "price",
			"value": req.Price,
		})
	}

	if req.StockQuantity < 0 {
		return errors.NewValidationError("stock quantity cannot be negative", map[string]interface{}{
			"field": "stock_quantity",
			"value": req.StockQuantity,
		})
	}

	if req.Status != model.ProductStatusActive && req.Status != model.ProductStatusInactive {
		return errors.NewValidationError("invalid product status", map[string]interface{}{
			"field": "status",
			"value": req.Status,
		})
	}

	return nil
}

func (s *productService) validateCreateCategoryRequest(req *model.CreateCategoryRequest) error {
	if strings.TrimSpace(req.Name) == "" {
		return errors.NewValidationError("category name is required", map[string]interface{}{
			"field": "name",
		})
	}

	return nil
}
