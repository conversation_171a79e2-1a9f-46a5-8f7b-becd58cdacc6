package middleware

import (
	"context"
	"path"
	"slices"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var publicMethods = map[string]bool{
	"HealthCheck": true,
}

var methodAllowList = map[string][]string{
	"api-gateway": {"CreateProduct", "GetProduct", "UpdateProduct", "DeleteProduct", "ListProducts", "CreateCategory", "GetCategory", "ListCategories"},
	"voucher-service": {"GetProduct", "ListProducts", "GetCategory", "ListCategories"},
	"order-service": {"GetProduct", "ListProducts", "GetCategory", "ListCategories"},
}

func CreateServiceAuthFunc(jwtManager *auth.<PERSON>, currentServiceClientID string) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		method := path.Base(info.FullMethod)

		if publicMethods[method] {
			return handler(ctx, req)
		}

		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, status.Errorf(codes.Unauthenticated, "missing metadata")
		}

		clientIDs := md.Get("client-id")
		if len(clientIDs) == 0 {
			return nil, status.Errorf(codes.Unauthenticated, "missing client-id")
		}
		clientID := clientIDs[0]

		if clientID == currentServiceClientID {
			return nil, status.Errorf(codes.PermissionDenied, "service cannot call itself")
		}

		allowedMethods, exists := methodAllowList[clientID]
		if !exists {
			return nil, status.Errorf(codes.PermissionDenied, "client not authorized")
		}

		if !slices.Contains(allowedMethods, method) {
			return nil, status.Errorf(codes.PermissionDenied, "method not allowed for client")
		}

		return handler(ctx, req)
	}
}
