package clients

import (
	"context"
	"fmt"
	"time"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type AuthClient struct {
	client proto_auth_v1.AuthServiceClient
	conn   *shared_grpc.Client
}

func NewAuthClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*AuthClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for auth service: %w", err)
	}

	return &AuthClient{
		client: proto_auth_v1.NewAuthServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *AuthClient) Close() {
	c.conn.Close()
}

func (c *AuthClient) ValidateServiceCredentials(ctx context.Context, clientID, clientKey string) (string, error) {
	req := &proto_auth_v1.ValidateServiceCredentialsRequest{
		ClientId:  clientID,
		ClientKey: clientKey,
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.ValidateServiceCredentials(ctx, req)
	if err != nil {
		return "", err
	}
	if !resp.Valid {
		return "", fmt.Errorf("invalid service credentials")
	}
	return resp.ServiceName, nil
}
