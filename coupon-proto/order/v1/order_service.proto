syntax = "proto3";

package order.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "common/v1/pagination.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1";

service OrderService {
  // Internal order management APIs used for service-to-service communication
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
  rpc GetOrder(GetOrderRequest) returns (GetOrderResponse);
  rpc UpdateOrderStatus(UpdateOrderStatusRequest) returns (UpdateOrderStatusResponse);
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);

  // User statistics APIs for voucher eligibility checking
  rpc GetUserOrderCount(GetUserOrderCountRequest) returns (GetUserOrderCountResponse);
  rpc GetUserVoucherUsageCount(GetUserVoucherUsageCountRequest) returns (GetUserVoucherUsageCountResponse);

  // Health check
  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message OrderItem {
  string product_id = 1;
  int32 quantity = 2;
  double price = 3;
}

message Order {
  string id = 1;
  string user_id = 2;
  repeated OrderItem items = 3;
  double total_amount = 4;
  string status = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  optional string applied_voucher_id = 8;
  optional double discount_amount = 9;
}

message CreateOrderRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  repeated OrderItem items = 3;
}

message CreateOrderResponse {
  common.v1.ResponseMetadata metadata = 1;
  Order order = 2;
  common.v1.ServiceError error = 3;
}

message GetOrderRequest {
  common.v1.RequestMetadata metadata = 1;
  string order_id = 2;
}

message GetOrderResponse {
  common.v1.ResponseMetadata metadata = 1;
  Order order = 2;
  common.v1.ServiceError error = 3;
}

message UpdateOrderStatusRequest {
  common.v1.RequestMetadata metadata = 1;
  string order_id = 2;
  string status = 3;
}

message UpdateOrderStatusResponse {
  common.v1.ResponseMetadata metadata = 1;
  Order order = 2;
  common.v1.ServiceError error = 3;
}

message ListOrdersRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  common.v1.PaginationRequest pagination = 3;
}

message ListOrdersResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Order orders = 2;
  common.v1.PaginationResponse pagination = 3;
  common.v1.ServiceError error = 4;
}

message GetUserOrderCountRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
}

message GetUserOrderCountResponse {
  common.v1.ResponseMetadata metadata = 1;
  int64 order_count = 2;
  common.v1.ServiceError error = 3;
}

message GetUserVoucherUsageCountRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  string voucher_id = 3;
}

message GetUserVoucherUsageCountResponse {
  common.v1.ResponseMetadata metadata = 1;
  int32 usage_count = 2;
  common.v1.ServiceError error = 3;
}
