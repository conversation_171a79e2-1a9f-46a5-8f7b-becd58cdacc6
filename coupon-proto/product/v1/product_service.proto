syntax = "proto3";

package product.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "common/v1/pagination.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1";

service ProductService {
  rpc GetProduct(GetProductRequest) returns (GetProductResponse);
  rpc UpdateProduct(UpdateProductRequest) returns (UpdateProductResponse);
  rpc ListProducts(ListProductsRequest) returns (ListProductsResponse);

  rpc ListCategories(ListCategoriesRequest) returns (ListCategoriesResponse);

  rpc HealthCheck(common.v1.HealthCheckRequest) 
    returns (common.v1.HealthCheckResponse);
}

enum ProductStatus {
  PRODUCT_STATUS_UNSPECIFIED = 0;
  PRODUCT_STATUS_ACTIVE = 1;
  PRODUCT_STATUS_INACTIVE = 2;
}

message Category {
  string id = 1;
  string name = 2;
  string description = 3;
  google.protobuf.Timestamp created_at = 4;
}

message Product {
  string id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
  string category_id = 5;
  string image_url = 6;
  int64 stock_quantity = 7;
  ProductStatus status = 8;
  string brand = 9;
  string sku = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;

  Category category = 13;
}

message GetProductRequest {
  common.v1.RequestMetadata metadata = 1;
  string product_id = 2;
}

message GetProductResponse {
  common.v1.ResponseMetadata metadata = 1;
  Product product = 2;
  common.v1.ServiceError error = 3;
}

message UpdateProductRequest {
  common.v1.RequestMetadata metadata = 1;
  string product_id = 2;
  string name = 3;
  string description = 4;
  double price = 5;
  string category_id = 6;
  string image_url = 7;
  int64 stock_quantity = 8;
  ProductStatus status = 9;
  string brand = 10;
  string sku = 11;
}

message UpdateProductResponse {
  common.v1.ResponseMetadata metadata = 1;
  Product product = 2;
  common.v1.ServiceError error = 3;
}

message ListProductsRequest {
  common.v1.RequestMetadata metadata = 1;
  common.v1.PaginationRequest pagination = 2;
  string search = 3;
  string category_id = 4;
  ProductStatus status = 5;
  string sort_by = 6;
  string sort_order = 7;
}

message ListProductsResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Product products = 2;
  common.v1.PaginationResponse pagination = 3;
  common.v1.ServiceError error = 4;
}

message ListCategoriesRequest {
  common.v1.RequestMetadata metadata = 1;
  common.v1.PaginationRequest pagination = 2;
  string search = 3;
}

message ListCategoriesResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated Category categories = 2;
  common.v1.PaginationResponse pagination = 3;
  common.v1.ServiceError error = 4;
}
