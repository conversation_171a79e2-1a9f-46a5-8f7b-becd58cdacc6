// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: voucher/v1/voucher_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UsageMethod int32

const (
	UsageMethod_USAGE_METHOD_UNSPECIFIED UsageMethod = 0
	UsageMethod_USAGE_METHOD_MANUAL      UsageMethod = 1
	UsageMethod_USAGE_METHOD_AUTO        UsageMethod = 2
)

// Enum value maps for UsageMethod.
var (
	UsageMethod_name = map[int32]string{
		0: "USAGE_METHOD_UNSPECIFIED",
		1: "USAGE_METHOD_MANUAL",
		2: "USAGE_METHOD_AUTO",
	}
	UsageMethod_value = map[string]int32{
		"USAGE_METHOD_UNSPECIFIED": 0,
		"USAGE_METHOD_MANUAL":      1,
		"USAGE_METHOD_AUTO":        2,
	}
)

func (x UsageMethod) Enum() *UsageMethod {
	p := new(UsageMethod)
	*p = x
	return p
}

func (x UsageMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UsageMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_voucher_v1_voucher_service_proto_enumTypes[0].Descriptor()
}

func (UsageMethod) Type() protoreflect.EnumType {
	return &file_voucher_v1_voucher_service_proto_enumTypes[0]
}

func (x UsageMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UsageMethod.Descriptor instead.
func (UsageMethod) EnumDescriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{0}
}

type VoucherStatus int32

const (
	VoucherStatus_VOUCHER_STATUS_UNSPECIFIED VoucherStatus = 0
	VoucherStatus_VOUCHER_STATUS_ACTIVE      VoucherStatus = 1
	VoucherStatus_VOUCHER_STATUS_INACTIVE    VoucherStatus = 2
	VoucherStatus_VOUCHER_STATUS_EXPIRED     VoucherStatus = 3
)

// Enum value maps for VoucherStatus.
var (
	VoucherStatus_name = map[int32]string{
		0: "VOUCHER_STATUS_UNSPECIFIED",
		1: "VOUCHER_STATUS_ACTIVE",
		2: "VOUCHER_STATUS_INACTIVE",
		3: "VOUCHER_STATUS_EXPIRED",
	}
	VoucherStatus_value = map[string]int32{
		"VOUCHER_STATUS_UNSPECIFIED": 0,
		"VOUCHER_STATUS_ACTIVE":      1,
		"VOUCHER_STATUS_INACTIVE":    2,
		"VOUCHER_STATUS_EXPIRED":     3,
	}
)

func (x VoucherStatus) Enum() *VoucherStatus {
	p := new(VoucherStatus)
	*p = x
	return p
}

func (x VoucherStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VoucherStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_voucher_v1_voucher_service_proto_enumTypes[1].Descriptor()
}

func (VoucherStatus) Type() protoreflect.EnumType {
	return &file_voucher_v1_voucher_service_proto_enumTypes[1]
}

func (x VoucherStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VoucherStatus.Descriptor instead.
func (VoucherStatus) EnumDescriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{1}
}

type TimeRestrictionType int32

const (
	TimeRestrictionType_TIME_RESTRICTION_TYPE_UNSPECIFIED     TimeRestrictionType = 0
	TimeRestrictionType_TIME_RESTRICTION_TYPE_DAYS_OF_WEEK    TimeRestrictionType = 1
	TimeRestrictionType_TIME_RESTRICTION_TYPE_HOURS_OF_DAY    TimeRestrictionType = 2
	TimeRestrictionType_TIME_RESTRICTION_TYPE_SPECIFIC_DATES  TimeRestrictionType = 3
	TimeRestrictionType_TIME_RESTRICTION_TYPE_RECURRING_DATES TimeRestrictionType = 4
)

// Enum value maps for TimeRestrictionType.
var (
	TimeRestrictionType_name = map[int32]string{
		0: "TIME_RESTRICTION_TYPE_UNSPECIFIED",
		1: "TIME_RESTRICTION_TYPE_DAYS_OF_WEEK",
		2: "TIME_RESTRICTION_TYPE_HOURS_OF_DAY",
		3: "TIME_RESTRICTION_TYPE_SPECIFIC_DATES",
		4: "TIME_RESTRICTION_TYPE_RECURRING_DATES",
	}
	TimeRestrictionType_value = map[string]int32{
		"TIME_RESTRICTION_TYPE_UNSPECIFIED":     0,
		"TIME_RESTRICTION_TYPE_DAYS_OF_WEEK":    1,
		"TIME_RESTRICTION_TYPE_HOURS_OF_DAY":    2,
		"TIME_RESTRICTION_TYPE_SPECIFIC_DATES":  3,
		"TIME_RESTRICTION_TYPE_RECURRING_DATES": 4,
	}
)

func (x TimeRestrictionType) Enum() *TimeRestrictionType {
	p := new(TimeRestrictionType)
	*p = x
	return p
}

func (x TimeRestrictionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeRestrictionType) Descriptor() protoreflect.EnumDescriptor {
	return file_voucher_v1_voucher_service_proto_enumTypes[2].Descriptor()
}

func (TimeRestrictionType) Type() protoreflect.EnumType {
	return &file_voucher_v1_voucher_service_proto_enumTypes[2]
}

func (x TimeRestrictionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeRestrictionType.Descriptor instead.
func (TimeRestrictionType) EnumDescriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{2}
}

type RecurrencePattern int32

const (
	RecurrencePattern_RECURRENCE_PATTERN_UNSPECIFIED RecurrencePattern = 0
	RecurrencePattern_RECURRENCE_PATTERN_DAILY       RecurrencePattern = 1
	RecurrencePattern_RECURRENCE_PATTERN_WEEKLY      RecurrencePattern = 2
	RecurrencePattern_RECURRENCE_PATTERN_MONTHLY     RecurrencePattern = 3
	RecurrencePattern_RECURRENCE_PATTERN_QUARTERLY   RecurrencePattern = 4
	RecurrencePattern_RECURRENCE_PATTERN_YEARLY      RecurrencePattern = 5
)

// Enum value maps for RecurrencePattern.
var (
	RecurrencePattern_name = map[int32]string{
		0: "RECURRENCE_PATTERN_UNSPECIFIED",
		1: "RECURRENCE_PATTERN_DAILY",
		2: "RECURRENCE_PATTERN_WEEKLY",
		3: "RECURRENCE_PATTERN_MONTHLY",
		4: "RECURRENCE_PATTERN_QUARTERLY",
		5: "RECURRENCE_PATTERN_YEARLY",
	}
	RecurrencePattern_value = map[string]int32{
		"RECURRENCE_PATTERN_UNSPECIFIED": 0,
		"RECURRENCE_PATTERN_DAILY":       1,
		"RECURRENCE_PATTERN_WEEKLY":      2,
		"RECURRENCE_PATTERN_MONTHLY":     3,
		"RECURRENCE_PATTERN_QUARTERLY":   4,
		"RECURRENCE_PATTERN_YEARLY":      5,
	}
)

func (x RecurrencePattern) Enum() *RecurrencePattern {
	p := new(RecurrencePattern)
	*p = x
	return p
}

func (x RecurrencePattern) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecurrencePattern) Descriptor() protoreflect.EnumDescriptor {
	return file_voucher_v1_voucher_service_proto_enumTypes[3].Descriptor()
}

func (RecurrencePattern) Type() protoreflect.EnumType {
	return &file_voucher_v1_voucher_service_proto_enumTypes[3]
}

func (x RecurrencePattern) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecurrencePattern.Descriptor instead.
func (RecurrencePattern) EnumDescriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{3}
}

type DiscountType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TypeCode      string                 `protobuf:"bytes,2,opt,name=type_code,json=typeCode,proto3" json:"type_code,omitempty"`
	TypeName      string                 `protobuf:"bytes,3,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DiscountType) Reset() {
	*x = DiscountType{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DiscountType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscountType) ProtoMessage() {}

func (x *DiscountType) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscountType.ProtoReflect.Descriptor instead.
func (*DiscountType) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{0}
}

func (x *DiscountType) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DiscountType) GetTypeCode() string {
	if x != nil {
		return x.TypeCode
	}
	return ""
}

func (x *DiscountType) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *DiscountType) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DiscountType) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *DiscountType) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DiscountType) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type CartItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProductId     *string                `protobuf:"bytes,1,opt,name=product_id,json=productId,proto3,oneof" json:"product_id,omitempty"`
	CategoryId    *string                `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	Quantity      int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Price         float64                `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CartItem) Reset() {
	*x = CartItem{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CartItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CartItem) ProtoMessage() {}

func (x *CartItem) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CartItem.ProtoReflect.Descriptor instead.
func (*CartItem) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{1}
}

func (x *CartItem) GetProductId() string {
	if x != nil && x.ProductId != nil {
		return *x.ProductId
	}
	return ""
}

func (x *CartItem) GetCategoryId() string {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return ""
}

func (x *CartItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CartItem) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type VoucherProductRestriction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	VoucherId     string                 `protobuf:"bytes,3,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	ProductId     *string                `protobuf:"bytes,4,opt,name=product_id,json=productId,proto3,oneof" json:"product_id,omitempty"`
	CategoryId    *string                `protobuf:"bytes,5,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	ProductName   *string                `protobuf:"bytes,6,opt,name=product_name,json=productName,proto3,oneof" json:"product_name,omitempty"`
	CategoryName  *string                `protobuf:"bytes,7,opt,name=category_name,json=categoryName,proto3,oneof" json:"category_name,omitempty"`
	IsIncluded    bool                   `protobuf:"varint,8,opt,name=is_included,json=isIncluded,proto3" json:"is_included,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VoucherProductRestriction) Reset() {
	*x = VoucherProductRestriction{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherProductRestriction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherProductRestriction) ProtoMessage() {}

func (x *VoucherProductRestriction) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherProductRestriction.ProtoReflect.Descriptor instead.
func (*VoucherProductRestriction) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{2}
}

func (x *VoucherProductRestriction) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VoucherProductRestriction) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VoucherProductRestriction) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

func (x *VoucherProductRestriction) GetProductId() string {
	if x != nil && x.ProductId != nil {
		return *x.ProductId
	}
	return ""
}

func (x *VoucherProductRestriction) GetCategoryId() string {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return ""
}

func (x *VoucherProductRestriction) GetProductName() string {
	if x != nil && x.ProductName != nil {
		return *x.ProductName
	}
	return ""
}

func (x *VoucherProductRestriction) GetCategoryName() string {
	if x != nil && x.CategoryName != nil {
		return *x.CategoryName
	}
	return ""
}

func (x *VoucherProductRestriction) GetIsIncluded() bool {
	if x != nil {
		return x.IsIncluded
	}
	return false
}

type VoucherTimeRestriction struct {
	state                protoimpl.MessageState   `protogen:"open.v1"`
	Id                   string                   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt            *timestamppb.Timestamp   `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	VoucherId            string                   `protobuf:"bytes,3,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	RestrictionType      TimeRestrictionType      `protobuf:"varint,4,opt,name=restriction_type,json=restrictionType,proto3,enum=voucher.v1.TimeRestrictionType" json:"restriction_type,omitempty"`
	AllowedDaysOfWeeks   []int32                  `protobuf:"varint,5,rep,packed,name=allowed_days_of_weeks,json=allowedDaysOfWeeks,proto3" json:"allowed_days_of_weeks,omitempty"`
	AllowedHoursStart    *int32                   `protobuf:"varint,6,opt,name=allowed_hours_start,json=allowedHoursStart,proto3,oneof" json:"allowed_hours_start,omitempty"`
	AllowedHoursEnd      *int32                   `protobuf:"varint,7,opt,name=allowed_hours_end,json=allowedHoursEnd,proto3,oneof" json:"allowed_hours_end,omitempty"`
	SpecificDates        []*timestamppb.Timestamp `protobuf:"bytes,8,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	RecurrencePattern    *RecurrencePattern       `protobuf:"varint,9,opt,name=recurrence_pattern,json=recurrencePattern,proto3,enum=voucher.v1.RecurrencePattern,oneof" json:"recurrence_pattern,omitempty"`
	RecurrenceDayOfMonth *int32                   `protobuf:"varint,10,opt,name=recurrence_day_of_month,json=recurrenceDayOfMonth,proto3,oneof" json:"recurrence_day_of_month,omitempty"`
	RecurrenceMonth      *int32                   `protobuf:"varint,11,opt,name=recurrence_month,json=recurrenceMonth,proto3,oneof" json:"recurrence_month,omitempty"`
	RecurrenceDayOfWeek  *int32                   `protobuf:"varint,12,opt,name=recurrence_day_of_week,json=recurrenceDayOfWeek,proto3,oneof" json:"recurrence_day_of_week,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *VoucherTimeRestriction) Reset() {
	*x = VoucherTimeRestriction{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherTimeRestriction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherTimeRestriction) ProtoMessage() {}

func (x *VoucherTimeRestriction) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherTimeRestriction.ProtoReflect.Descriptor instead.
func (*VoucherTimeRestriction) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{3}
}

func (x *VoucherTimeRestriction) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VoucherTimeRestriction) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VoucherTimeRestriction) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

func (x *VoucherTimeRestriction) GetRestrictionType() TimeRestrictionType {
	if x != nil {
		return x.RestrictionType
	}
	return TimeRestrictionType_TIME_RESTRICTION_TYPE_UNSPECIFIED
}

func (x *VoucherTimeRestriction) GetAllowedDaysOfWeeks() []int32 {
	if x != nil {
		return x.AllowedDaysOfWeeks
	}
	return nil
}

func (x *VoucherTimeRestriction) GetAllowedHoursStart() int32 {
	if x != nil && x.AllowedHoursStart != nil {
		return *x.AllowedHoursStart
	}
	return 0
}

func (x *VoucherTimeRestriction) GetAllowedHoursEnd() int32 {
	if x != nil && x.AllowedHoursEnd != nil {
		return *x.AllowedHoursEnd
	}
	return 0
}

func (x *VoucherTimeRestriction) GetSpecificDates() []*timestamppb.Timestamp {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *VoucherTimeRestriction) GetRecurrencePattern() RecurrencePattern {
	if x != nil && x.RecurrencePattern != nil {
		return *x.RecurrencePattern
	}
	return RecurrencePattern_RECURRENCE_PATTERN_UNSPECIFIED
}

func (x *VoucherTimeRestriction) GetRecurrenceDayOfMonth() int32 {
	if x != nil && x.RecurrenceDayOfMonth != nil {
		return *x.RecurrenceDayOfMonth
	}
	return 0
}

func (x *VoucherTimeRestriction) GetRecurrenceMonth() int32 {
	if x != nil && x.RecurrenceMonth != nil {
		return *x.RecurrenceMonth
	}
	return 0
}

func (x *VoucherTimeRestriction) GetRecurrenceDayOfWeek() int32 {
	if x != nil && x.RecurrenceDayOfWeek != nil {
		return *x.RecurrenceDayOfWeek
	}
	return 0
}

type VoucherUserEligibility struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	VoucherId         string                 `protobuf:"bytes,3,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	UserId            *string                `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`
	UserType          *string                `protobuf:"bytes,5,opt,name=user_type,json=userType,proto3,oneof" json:"user_type,omitempty"`
	MinAccountAgeDays *int32                 `protobuf:"varint,6,opt,name=min_account_age_days,json=minAccountAgeDays,proto3,oneof" json:"min_account_age_days,omitempty"`
	MaxAccountAgeDays *int32                 `protobuf:"varint,7,opt,name=max_account_age_days,json=maxAccountAgeDays,proto3,oneof" json:"max_account_age_days,omitempty"`
	MinPreviousOrders *int64                 `protobuf:"varint,8,opt,name=min_previous_orders,json=minPreviousOrders,proto3,oneof" json:"min_previous_orders,omitempty"`
	MaxPreviousOrders *int64                 `protobuf:"varint,9,opt,name=max_previous_orders,json=maxPreviousOrders,proto3,oneof" json:"max_previous_orders,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *VoucherUserEligibility) Reset() {
	*x = VoucherUserEligibility{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherUserEligibility) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherUserEligibility) ProtoMessage() {}

func (x *VoucherUserEligibility) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherUserEligibility.ProtoReflect.Descriptor instead.
func (*VoucherUserEligibility) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{4}
}

func (x *VoucherUserEligibility) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VoucherUserEligibility) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VoucherUserEligibility) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

func (x *VoucherUserEligibility) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

func (x *VoucherUserEligibility) GetUserType() string {
	if x != nil && x.UserType != nil {
		return *x.UserType
	}
	return ""
}

func (x *VoucherUserEligibility) GetMinAccountAgeDays() int32 {
	if x != nil && x.MinAccountAgeDays != nil {
		return *x.MinAccountAgeDays
	}
	return 0
}

func (x *VoucherUserEligibility) GetMaxAccountAgeDays() int32 {
	if x != nil && x.MaxAccountAgeDays != nil {
		return *x.MaxAccountAgeDays
	}
	return 0
}

func (x *VoucherUserEligibility) GetMinPreviousOrders() int64 {
	if x != nil && x.MinPreviousOrders != nil {
		return *x.MinPreviousOrders
	}
	return 0
}

func (x *VoucherUserEligibility) GetMaxPreviousOrders() int64 {
	if x != nil && x.MaxPreviousOrders != nil {
		return *x.MaxPreviousOrders
	}
	return 0
}

type VoucherOrderUsage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UsedAt        *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=used_at,json=usedAt,proto3" json:"used_at,omitempty"`
	OrderAmount   float64                `protobuf:"fixed64,3,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VoucherOrderUsage) Reset() {
	*x = VoucherOrderUsage{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VoucherOrderUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoucherOrderUsage) ProtoMessage() {}

func (x *VoucherOrderUsage) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoucherOrderUsage.ProtoReflect.Descriptor instead.
func (*VoucherOrderUsage) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{5}
}

func (x *VoucherOrderUsage) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *VoucherOrderUsage) GetUsedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UsedAt
	}
	return nil
}

func (x *VoucherOrderUsage) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *VoucherOrderUsage) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type UserVoucherUsage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UsageCount    int32                  `protobuf:"varint,2,opt,name=usage_count,json=usageCount,proto3" json:"usage_count,omitempty"`
	FullName      string                 `protobuf:"bytes,3,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Type          string                 `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Orders        []*VoucherOrderUsage   `protobuf:"bytes,6,rep,name=orders,proto3" json:"orders,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserVoucherUsage) Reset() {
	*x = UserVoucherUsage{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserVoucherUsage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserVoucherUsage) ProtoMessage() {}

func (x *UserVoucherUsage) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserVoucherUsage.ProtoReflect.Descriptor instead.
func (*UserVoucherUsage) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{6}
}

func (x *UserVoucherUsage) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserVoucherUsage) GetUsageCount() int32 {
	if x != nil {
		return x.UsageCount
	}
	return 0
}

func (x *UserVoucherUsage) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UserVoucherUsage) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserVoucherUsage) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserVoucherUsage) GetOrders() []*VoucherOrderUsage {
	if x != nil {
		return x.Orders
	}
	return nil
}

type Voucher struct {
	state                protoimpl.MessageState       `protogen:"open.v1"`
	Id                   string                       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	VoucherCode          string                       `protobuf:"bytes,2,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	Title                string                       `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description          string                       `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	DiscountTypeId       string                       `protobuf:"bytes,5,opt,name=discount_type_id,json=discountTypeId,proto3" json:"discount_type_id,omitempty"`
	DiscountValue        float64                      `protobuf:"fixed64,6,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	UsageMethod          UsageMethod                  `protobuf:"varint,7,opt,name=usage_method,json=usageMethod,proto3,enum=voucher.v1.UsageMethod" json:"usage_method,omitempty"`
	ValidFrom            *timestamppb.Timestamp       `protobuf:"bytes,8,opt,name=valid_from,json=validFrom,proto3" json:"valid_from,omitempty"`
	ValidUntil           *timestamppb.Timestamp       `protobuf:"bytes,9,opt,name=valid_until,json=validUntil,proto3" json:"valid_until,omitempty"`
	MaxUsageCount        *int32                       `protobuf:"varint,10,opt,name=max_usage_count,json=maxUsageCount,proto3,oneof" json:"max_usage_count,omitempty"`
	MaxUsagePerUser      *int32                       `protobuf:"varint,11,opt,name=max_usage_per_user,json=maxUsagePerUser,proto3,oneof" json:"max_usage_per_user,omitempty"`
	UserEligibilityType  string                       `protobuf:"bytes,12,opt,name=user_eligibility_type,json=userEligibilityType,proto3" json:"user_eligibility_type,omitempty"`
	CurrentUsageCount    int32                        `protobuf:"varint,13,opt,name=current_usage_count,json=currentUsageCount,proto3" json:"current_usage_count,omitempty"`
	MinOrderAmount       float64                      `protobuf:"fixed64,14,opt,name=min_order_amount,json=minOrderAmount,proto3" json:"min_order_amount,omitempty"`
	MaxDiscountAmount    *float64                     `protobuf:"fixed64,15,opt,name=max_discount_amount,json=maxDiscountAmount,proto3,oneof" json:"max_discount_amount,omitempty"`
	CreatedBy            string                       `protobuf:"bytes,16,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt            *timestamppb.Timestamp       `protobuf:"bytes,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp       `protobuf:"bytes,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DiscountType         *DiscountType                `protobuf:"bytes,19,opt,name=discount_type,json=discountType,proto3,oneof" json:"discount_type,omitempty"`
	Status               VoucherStatus                `protobuf:"varint,20,opt,name=status,proto3,enum=voucher.v1.VoucherStatus" json:"status,omitempty"`
	ProductRestrictions  []*VoucherProductRestriction `protobuf:"bytes,21,rep,name=product_restrictions,json=productRestrictions,proto3" json:"product_restrictions,omitempty"`
	TimeRestrictions     []*VoucherTimeRestriction    `protobuf:"bytes,22,rep,name=time_restrictions,json=timeRestrictions,proto3" json:"time_restrictions,omitempty"`
	UserEligibilityRules []*VoucherUserEligibility    `protobuf:"bytes,23,rep,name=user_eligibility_rules,json=userEligibilityRules,proto3" json:"user_eligibility_rules,omitempty"`
	UserUsages           []*UserVoucherUsage          `protobuf:"bytes,24,rep,name=user_usages,json=userUsages,proto3" json:"user_usages,omitempty"`
	TotalSavings         float64                      `protobuf:"fixed64,25,opt,name=total_savings,json=totalSavings,proto3" json:"total_savings,omitempty"`
	UniqueUsers          int32                        `protobuf:"varint,26,opt,name=unique_users,json=uniqueUsers,proto3" json:"unique_users,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Voucher) Reset() {
	*x = Voucher{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Voucher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Voucher) ProtoMessage() {}

func (x *Voucher) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Voucher.ProtoReflect.Descriptor instead.
func (*Voucher) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{7}
}

func (x *Voucher) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Voucher) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

func (x *Voucher) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Voucher) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Voucher) GetDiscountTypeId() string {
	if x != nil {
		return x.DiscountTypeId
	}
	return ""
}

func (x *Voucher) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *Voucher) GetUsageMethod() UsageMethod {
	if x != nil {
		return x.UsageMethod
	}
	return UsageMethod_USAGE_METHOD_UNSPECIFIED
}

func (x *Voucher) GetValidFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidFrom
	}
	return nil
}

func (x *Voucher) GetValidUntil() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidUntil
	}
	return nil
}

func (x *Voucher) GetMaxUsageCount() int32 {
	if x != nil && x.MaxUsageCount != nil {
		return *x.MaxUsageCount
	}
	return 0
}

func (x *Voucher) GetMaxUsagePerUser() int32 {
	if x != nil && x.MaxUsagePerUser != nil {
		return *x.MaxUsagePerUser
	}
	return 0
}

func (x *Voucher) GetUserEligibilityType() string {
	if x != nil {
		return x.UserEligibilityType
	}
	return ""
}

func (x *Voucher) GetCurrentUsageCount() int32 {
	if x != nil {
		return x.CurrentUsageCount
	}
	return 0
}

func (x *Voucher) GetMinOrderAmount() float64 {
	if x != nil {
		return x.MinOrderAmount
	}
	return 0
}

func (x *Voucher) GetMaxDiscountAmount() float64 {
	if x != nil && x.MaxDiscountAmount != nil {
		return *x.MaxDiscountAmount
	}
	return 0
}

func (x *Voucher) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Voucher) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Voucher) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Voucher) GetDiscountType() *DiscountType {
	if x != nil {
		return x.DiscountType
	}
	return nil
}

func (x *Voucher) GetStatus() VoucherStatus {
	if x != nil {
		return x.Status
	}
	return VoucherStatus_VOUCHER_STATUS_UNSPECIFIED
}

func (x *Voucher) GetProductRestrictions() []*VoucherProductRestriction {
	if x != nil {
		return x.ProductRestrictions
	}
	return nil
}

func (x *Voucher) GetTimeRestrictions() []*VoucherTimeRestriction {
	if x != nil {
		return x.TimeRestrictions
	}
	return nil
}

func (x *Voucher) GetUserEligibilityRules() []*VoucherUserEligibility {
	if x != nil {
		return x.UserEligibilityRules
	}
	return nil
}

func (x *Voucher) GetUserUsages() []*UserVoucherUsage {
	if x != nil {
		return x.UserUsages
	}
	return nil
}

func (x *Voucher) GetTotalSavings() float64 {
	if x != nil {
		return x.TotalSavings
	}
	return 0
}

func (x *Voucher) GetUniqueUsers() int32 {
	if x != nil {
		return x.UniqueUsers
	}
	return 0
}

type CreateVoucherRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Metadata          *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	VoucherCode       string                 `protobuf:"bytes,2,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	Title             string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description       string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	DiscountTypeId    string                 `protobuf:"bytes,5,opt,name=discount_type_id,json=discountTypeId,proto3" json:"discount_type_id,omitempty"`
	DiscountValue     float64                `protobuf:"fixed64,6,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	UsageMethod       UsageMethod            `protobuf:"varint,7,opt,name=usage_method,json=usageMethod,proto3,enum=voucher.v1.UsageMethod" json:"usage_method,omitempty"`
	ValidFrom         *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=valid_from,json=validFrom,proto3" json:"valid_from,omitempty"`
	ValidUntil        *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=valid_until,json=validUntil,proto3" json:"valid_until,omitempty"`
	MaxUsageCount     *int32                 `protobuf:"varint,10,opt,name=max_usage_count,json=maxUsageCount,proto3,oneof" json:"max_usage_count,omitempty"`
	MaxUsagePerUser   *int32                 `protobuf:"varint,11,opt,name=max_usage_per_user,json=maxUsagePerUser,proto3,oneof" json:"max_usage_per_user,omitempty"`
	MinOrderAmount    float64                `protobuf:"fixed64,12,opt,name=min_order_amount,json=minOrderAmount,proto3" json:"min_order_amount,omitempty"`
	MaxDiscountAmount *float64               `protobuf:"fixed64,13,opt,name=max_discount_amount,json=maxDiscountAmount,proto3,oneof" json:"max_discount_amount,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateVoucherRequest) Reset() {
	*x = CreateVoucherRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVoucherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVoucherRequest) ProtoMessage() {}

func (x *CreateVoucherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVoucherRequest.ProtoReflect.Descriptor instead.
func (*CreateVoucherRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateVoucherRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateVoucherRequest) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

func (x *CreateVoucherRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateVoucherRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateVoucherRequest) GetDiscountTypeId() string {
	if x != nil {
		return x.DiscountTypeId
	}
	return ""
}

func (x *CreateVoucherRequest) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *CreateVoucherRequest) GetUsageMethod() UsageMethod {
	if x != nil {
		return x.UsageMethod
	}
	return UsageMethod_USAGE_METHOD_UNSPECIFIED
}

func (x *CreateVoucherRequest) GetValidFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidFrom
	}
	return nil
}

func (x *CreateVoucherRequest) GetValidUntil() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidUntil
	}
	return nil
}

func (x *CreateVoucherRequest) GetMaxUsageCount() int32 {
	if x != nil && x.MaxUsageCount != nil {
		return *x.MaxUsageCount
	}
	return 0
}

func (x *CreateVoucherRequest) GetMaxUsagePerUser() int32 {
	if x != nil && x.MaxUsagePerUser != nil {
		return *x.MaxUsagePerUser
	}
	return 0
}

func (x *CreateVoucherRequest) GetMinOrderAmount() float64 {
	if x != nil {
		return x.MinOrderAmount
	}
	return 0
}

func (x *CreateVoucherRequest) GetMaxDiscountAmount() float64 {
	if x != nil && x.MaxDiscountAmount != nil {
		return *x.MaxDiscountAmount
	}
	return 0
}

type CreateVoucherResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Voucher       *Voucher               `protobuf:"bytes,2,opt,name=voucher,proto3" json:"voucher,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateVoucherResponse) Reset() {
	*x = CreateVoucherResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVoucherResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVoucherResponse) ProtoMessage() {}

func (x *CreateVoucherResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVoucherResponse.ProtoReflect.Descriptor instead.
func (*CreateVoucherResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{9}
}

func (x *CreateVoucherResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateVoucherResponse) GetVoucher() *Voucher {
	if x != nil {
		return x.Voucher
	}
	return nil
}

func (x *CreateVoucherResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetVoucherRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	VoucherId     string                 `protobuf:"bytes,2,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVoucherRequest) Reset() {
	*x = GetVoucherRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVoucherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoucherRequest) ProtoMessage() {}

func (x *GetVoucherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoucherRequest.ProtoReflect.Descriptor instead.
func (*GetVoucherRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetVoucherRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetVoucherRequest) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

type GetVoucherResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Voucher       *Voucher               `protobuf:"bytes,2,opt,name=voucher,proto3" json:"voucher,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVoucherResponse) Reset() {
	*x = GetVoucherResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVoucherResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoucherResponse) ProtoMessage() {}

func (x *GetVoucherResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoucherResponse.ProtoReflect.Descriptor instead.
func (*GetVoucherResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetVoucherResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetVoucherResponse) GetVoucher() *Voucher {
	if x != nil {
		return x.Voucher
	}
	return nil
}

func (x *GetVoucherResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetVoucherByCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	VoucherCode   string                 `protobuf:"bytes,2,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVoucherByCodeRequest) Reset() {
	*x = GetVoucherByCodeRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVoucherByCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoucherByCodeRequest) ProtoMessage() {}

func (x *GetVoucherByCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoucherByCodeRequest.ProtoReflect.Descriptor instead.
func (*GetVoucherByCodeRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetVoucherByCodeRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetVoucherByCodeRequest) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

type GetVoucherByCodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Voucher       *Voucher               `protobuf:"bytes,2,opt,name=voucher,proto3" json:"voucher,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVoucherByCodeResponse) Reset() {
	*x = GetVoucherByCodeResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVoucherByCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoucherByCodeResponse) ProtoMessage() {}

func (x *GetVoucherByCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoucherByCodeResponse.ProtoReflect.Descriptor instead.
func (*GetVoucherByCodeResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetVoucherByCodeResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetVoucherByCodeResponse) GetVoucher() *Voucher {
	if x != nil {
		return x.Voucher
	}
	return nil
}

func (x *GetVoucherByCodeResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type UpdateVoucherRequest struct {
	state               protoimpl.MessageState       `protogen:"open.v1"`
	Metadata            *v1.RequestMetadata          `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	VoucherId           string                       `protobuf:"bytes,2,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	Title               string                       `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description         string                       `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	DiscountTypeId      string                       `protobuf:"bytes,5,opt,name=discount_type_id,json=discountTypeId,proto3" json:"discount_type_id,omitempty"`
	DiscountValue       float64                      `protobuf:"fixed64,6,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	UsageMethod         UsageMethod                  `protobuf:"varint,7,opt,name=usage_method,json=usageMethod,proto3,enum=voucher.v1.UsageMethod" json:"usage_method,omitempty"`
	Status              VoucherStatus                `protobuf:"varint,8,opt,name=status,proto3,enum=voucher.v1.VoucherStatus" json:"status,omitempty"`
	MinOrderAmount      float64                      `protobuf:"fixed64,9,opt,name=min_order_amount,json=minOrderAmount,proto3" json:"min_order_amount,omitempty"`
	MaxDiscountAmount   *float64                     `protobuf:"fixed64,10,opt,name=max_discount_amount,json=maxDiscountAmount,proto3,oneof" json:"max_discount_amount,omitempty"`
	MaxUsageCount       *int32                       `protobuf:"varint,11,opt,name=max_usage_count,json=maxUsageCount,proto3,oneof" json:"max_usage_count,omitempty"`
	MaxUsagePerUser     *int32                       `protobuf:"varint,12,opt,name=max_usage_per_user,json=maxUsagePerUser,proto3,oneof" json:"max_usage_per_user,omitempty"`
	ValidFrom           *timestamppb.Timestamp       `protobuf:"bytes,13,opt,name=valid_from,json=validFrom,proto3" json:"valid_from,omitempty"`
	ValidUntil          *timestamppb.Timestamp       `protobuf:"bytes,14,opt,name=valid_until,json=validUntil,proto3" json:"valid_until,omitempty"`
	ProductRestrictions []*VoucherProductRestriction `protobuf:"bytes,15,rep,name=product_restrictions,json=productRestrictions,proto3" json:"product_restrictions,omitempty"`
	TimeRestrictions    []*VoucherTimeRestriction    `protobuf:"bytes,16,rep,name=time_restrictions,json=timeRestrictions,proto3" json:"time_restrictions,omitempty"`
	UserEligibilities   []*VoucherUserEligibility    `protobuf:"bytes,17,rep,name=user_eligibilities,json=userEligibilities,proto3" json:"user_eligibilities,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UpdateVoucherRequest) Reset() {
	*x = UpdateVoucherRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVoucherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVoucherRequest) ProtoMessage() {}

func (x *UpdateVoucherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVoucherRequest.ProtoReflect.Descriptor instead.
func (*UpdateVoucherRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateVoucherRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateVoucherRequest) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

func (x *UpdateVoucherRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateVoucherRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateVoucherRequest) GetDiscountTypeId() string {
	if x != nil {
		return x.DiscountTypeId
	}
	return ""
}

func (x *UpdateVoucherRequest) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *UpdateVoucherRequest) GetUsageMethod() UsageMethod {
	if x != nil {
		return x.UsageMethod
	}
	return UsageMethod_USAGE_METHOD_UNSPECIFIED
}

func (x *UpdateVoucherRequest) GetStatus() VoucherStatus {
	if x != nil {
		return x.Status
	}
	return VoucherStatus_VOUCHER_STATUS_UNSPECIFIED
}

func (x *UpdateVoucherRequest) GetMinOrderAmount() float64 {
	if x != nil {
		return x.MinOrderAmount
	}
	return 0
}

func (x *UpdateVoucherRequest) GetMaxDiscountAmount() float64 {
	if x != nil && x.MaxDiscountAmount != nil {
		return *x.MaxDiscountAmount
	}
	return 0
}

func (x *UpdateVoucherRequest) GetMaxUsageCount() int32 {
	if x != nil && x.MaxUsageCount != nil {
		return *x.MaxUsageCount
	}
	return 0
}

func (x *UpdateVoucherRequest) GetMaxUsagePerUser() int32 {
	if x != nil && x.MaxUsagePerUser != nil {
		return *x.MaxUsagePerUser
	}
	return 0
}

func (x *UpdateVoucherRequest) GetValidFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidFrom
	}
	return nil
}

func (x *UpdateVoucherRequest) GetValidUntil() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidUntil
	}
	return nil
}

func (x *UpdateVoucherRequest) GetProductRestrictions() []*VoucherProductRestriction {
	if x != nil {
		return x.ProductRestrictions
	}
	return nil
}

func (x *UpdateVoucherRequest) GetTimeRestrictions() []*VoucherTimeRestriction {
	if x != nil {
		return x.TimeRestrictions
	}
	return nil
}

func (x *UpdateVoucherRequest) GetUserEligibilities() []*VoucherUserEligibility {
	if x != nil {
		return x.UserEligibilities
	}
	return nil
}

type UpdateVoucherResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Voucher       *Voucher               `protobuf:"bytes,2,opt,name=voucher,proto3" json:"voucher,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVoucherResponse) Reset() {
	*x = UpdateVoucherResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVoucherResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVoucherResponse) ProtoMessage() {}

func (x *UpdateVoucherResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVoucherResponse.ProtoReflect.Descriptor instead.
func (*UpdateVoucherResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateVoucherResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateVoucherResponse) GetVoucher() *Voucher {
	if x != nil {
		return x.Voucher
	}
	return nil
}

func (x *UpdateVoucherResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ListVouchersRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Page           int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit          int32                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Search         string                 `protobuf:"bytes,4,opt,name=search,proto3" json:"search,omitempty"`
	DiscountTypeId *string                `protobuf:"bytes,5,opt,name=discount_type_id,json=discountTypeId,proto3,oneof" json:"discount_type_id,omitempty"`
	UsageMethod    *UsageMethod           `protobuf:"varint,6,opt,name=usage_method,json=usageMethod,proto3,enum=voucher.v1.UsageMethod,oneof" json:"usage_method,omitempty"`
	Status         string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	SortBy         string                 `protobuf:"bytes,8,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty"`
	SortOrder      string                 `protobuf:"bytes,9,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListVouchersRequest) Reset() {
	*x = ListVouchersRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVouchersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVouchersRequest) ProtoMessage() {}

func (x *ListVouchersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVouchersRequest.ProtoReflect.Descriptor instead.
func (*ListVouchersRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListVouchersRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListVouchersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListVouchersRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListVouchersRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *ListVouchersRequest) GetDiscountTypeId() string {
	if x != nil && x.DiscountTypeId != nil {
		return *x.DiscountTypeId
	}
	return ""
}

func (x *ListVouchersRequest) GetUsageMethod() UsageMethod {
	if x != nil && x.UsageMethod != nil {
		return *x.UsageMethod
	}
	return UsageMethod_USAGE_METHOD_UNSPECIFIED
}

func (x *ListVouchersRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListVouchersRequest) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *ListVouchersRequest) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

type ListVouchersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Vouchers      []*Voucher             `protobuf:"bytes,2,rep,name=vouchers,proto3" json:"vouchers,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	TotalPages    int32                  `protobuf:"varint,6,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListVouchersResponse) Reset() {
	*x = ListVouchersResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListVouchersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVouchersResponse) ProtoMessage() {}

func (x *ListVouchersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVouchersResponse.ProtoReflect.Descriptor instead.
func (*ListVouchersResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListVouchersResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListVouchersResponse) GetVouchers() []*Voucher {
	if x != nil {
		return x.Vouchers
	}
	return nil
}

func (x *ListVouchersResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListVouchersResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListVouchersResponse) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListVouchersResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *ListVouchersResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetDiscountTypesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDiscountTypesRequest) Reset() {
	*x = GetDiscountTypesRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDiscountTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountTypesRequest) ProtoMessage() {}

func (x *GetDiscountTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountTypesRequest.ProtoReflect.Descriptor instead.
func (*GetDiscountTypesRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetDiscountTypesRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type GetDiscountTypesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	DiscountTypes []*DiscountType        `protobuf:"bytes,2,rep,name=discount_types,json=discountTypes,proto3" json:"discount_types,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDiscountTypesResponse) Reset() {
	*x = GetDiscountTypesResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDiscountTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountTypesResponse) ProtoMessage() {}

func (x *GetDiscountTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountTypesResponse.ProtoReflect.Descriptor instead.
func (*GetDiscountTypesResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{19}
}

func (x *GetDiscountTypesResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetDiscountTypesResponse) GetDiscountTypes() []*DiscountType {
	if x != nil {
		return x.DiscountTypes
	}
	return nil
}

func (x *GetDiscountTypesResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckVoucherEligibilityRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	VoucherCode    string                 `protobuf:"bytes,2,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	UserId         string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderAmount    float64                `protobuf:"fixed64,4,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	OrderTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=order_timestamp,json=orderTimestamp,proto3" json:"order_timestamp,omitempty"`
	CartItems      []*CartItem            `protobuf:"bytes,6,rep,name=cart_items,json=cartItems,proto3" json:"cart_items,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckVoucherEligibilityRequest) Reset() {
	*x = CheckVoucherEligibilityRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVoucherEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVoucherEligibilityRequest) ProtoMessage() {}

func (x *CheckVoucherEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVoucherEligibilityRequest.ProtoReflect.Descriptor instead.
func (*CheckVoucherEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{20}
}

func (x *CheckVoucherEligibilityRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CheckVoucherEligibilityRequest) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

func (x *CheckVoucherEligibilityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CheckVoucherEligibilityRequest) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *CheckVoucherEligibilityRequest) GetOrderTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OrderTimestamp
	}
	return nil
}

func (x *CheckVoucherEligibilityRequest) GetCartItems() []*CartItem {
	if x != nil {
		return x.CartItems
	}
	return nil
}

type CheckVoucherEligibilityResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Eligible       bool                   `protobuf:"varint,2,opt,name=eligible,proto3" json:"eligible,omitempty"`
	Message        string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	VoucherId      string                 `protobuf:"bytes,4,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	DiscountAmount float64                `protobuf:"fixed64,5,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	Error          *v1.ServiceError       `protobuf:"bytes,6,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckVoucherEligibilityResponse) Reset() {
	*x = CheckVoucherEligibilityResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVoucherEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVoucherEligibilityResponse) ProtoMessage() {}

func (x *CheckVoucherEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVoucherEligibilityResponse.ProtoReflect.Descriptor instead.
func (*CheckVoucherEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{21}
}

func (x *CheckVoucherEligibilityResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CheckVoucherEligibilityResponse) GetEligible() bool {
	if x != nil {
		return x.Eligible
	}
	return false
}

func (x *CheckVoucherEligibilityResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckVoucherEligibilityResponse) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

func (x *CheckVoucherEligibilityResponse) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

func (x *CheckVoucherEligibilityResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type EligibleVoucherInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Eligible       bool                   `protobuf:"varint,1,opt,name=eligible,proto3" json:"eligible,omitempty"`
	Voucher        *Voucher               `protobuf:"bytes,2,opt,name=voucher,proto3" json:"voucher,omitempty"`
	DiscountAmount float64                `protobuf:"fixed64,3,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *EligibleVoucherInfo) Reset() {
	*x = EligibleVoucherInfo{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EligibleVoucherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EligibleVoucherInfo) ProtoMessage() {}

func (x *EligibleVoucherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EligibleVoucherInfo.ProtoReflect.Descriptor instead.
func (*EligibleVoucherInfo) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{22}
}

func (x *EligibleVoucherInfo) GetEligible() bool {
	if x != nil {
		return x.Eligible
	}
	return false
}

func (x *EligibleVoucherInfo) GetVoucher() *Voucher {
	if x != nil {
		return x.Voucher
	}
	return nil
}

func (x *EligibleVoucherInfo) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

type ListAutoEligibleVouchersRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId         string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderAmount    float64                `protobuf:"fixed64,3,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	OrderTimestamp *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=order_timestamp,json=orderTimestamp,proto3" json:"order_timestamp,omitempty"`
	CartItems      []*CartItem            `protobuf:"bytes,5,rep,name=cart_items,json=cartItems,proto3" json:"cart_items,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListAutoEligibleVouchersRequest) Reset() {
	*x = ListAutoEligibleVouchersRequest{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAutoEligibleVouchersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAutoEligibleVouchersRequest) ProtoMessage() {}

func (x *ListAutoEligibleVouchersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAutoEligibleVouchersRequest.ProtoReflect.Descriptor instead.
func (*ListAutoEligibleVouchersRequest) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{23}
}

func (x *ListAutoEligibleVouchersRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListAutoEligibleVouchersRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListAutoEligibleVouchersRequest) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *ListAutoEligibleVouchersRequest) GetOrderTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OrderTimestamp
	}
	return nil
}

func (x *ListAutoEligibleVouchersRequest) GetCartItems() []*CartItem {
	if x != nil {
		return x.CartItems
	}
	return nil
}

type ListAutoEligibleVouchersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Vouchers      []*EligibleVoucherInfo `protobuf:"bytes,2,rep,name=vouchers,proto3" json:"vouchers,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAutoEligibleVouchersResponse) Reset() {
	*x = ListAutoEligibleVouchersResponse{}
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAutoEligibleVouchersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAutoEligibleVouchersResponse) ProtoMessage() {}

func (x *ListAutoEligibleVouchersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_voucher_v1_voucher_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAutoEligibleVouchersResponse.ProtoReflect.Descriptor instead.
func (*ListAutoEligibleVouchersResponse) Descriptor() ([]byte, []int) {
	return file_voucher_v1_voucher_service_proto_rawDescGZIP(), []int{24}
}

func (x *ListAutoEligibleVouchersResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListAutoEligibleVouchersResponse) GetVouchers() []*EligibleVoucherInfo {
	if x != nil {
		return x.Vouchers
	}
	return nil
}

func (x *ListAutoEligibleVouchersResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_voucher_v1_voucher_service_proto protoreflect.FileDescriptor

const file_voucher_v1_voucher_service_proto_rawDesc = "" +
	"\n" +
	" voucher/v1/voucher_service.proto\x12\n" +
	"voucher.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8d\x02\n" +
	"\fDiscountType\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1b\n" +
	"\ttype_code\x18\x02 \x01(\tR\btypeCode\x12\x1b\n" +
	"\ttype_name\x18\x03 \x01(\tR\btypeName\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xa5\x01\n" +
	"\bCartItem\x12\"\n" +
	"\n" +
	"product_id\x18\x01 \x01(\tH\x00R\tproductId\x88\x01\x01\x12$\n" +
	"\vcategory_id\x18\x02 \x01(\tH\x01R\n" +
	"categoryId\x88\x01\x01\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x01R\x05priceB\r\n" +
	"\v_product_idB\x0e\n" +
	"\f_category_id\"\x84\x03\n" +
	"\x19VoucherProductRestriction\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x03 \x01(\tR\tvoucherId\x12\"\n" +
	"\n" +
	"product_id\x18\x04 \x01(\tH\x00R\tproductId\x88\x01\x01\x12$\n" +
	"\vcategory_id\x18\x05 \x01(\tH\x01R\n" +
	"categoryId\x88\x01\x01\x12&\n" +
	"\fproduct_name\x18\x06 \x01(\tH\x02R\vproductName\x88\x01\x01\x12(\n" +
	"\rcategory_name\x18\a \x01(\tH\x03R\fcategoryName\x88\x01\x01\x12\x1f\n" +
	"\vis_included\x18\b \x01(\bR\n" +
	"isIncludedB\r\n" +
	"\v_product_idB\x0e\n" +
	"\f_category_idB\x0f\n" +
	"\r_product_nameB\x10\n" +
	"\x0e_category_name\"\xb4\x06\n" +
	"\x16VoucherTimeRestriction\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x03 \x01(\tR\tvoucherId\x12J\n" +
	"\x10restriction_type\x18\x04 \x01(\x0e2\x1f.voucher.v1.TimeRestrictionTypeR\x0frestrictionType\x121\n" +
	"\x15allowed_days_of_weeks\x18\x05 \x03(\x05R\x12allowedDaysOfWeeks\x123\n" +
	"\x13allowed_hours_start\x18\x06 \x01(\x05H\x00R\x11allowedHoursStart\x88\x01\x01\x12/\n" +
	"\x11allowed_hours_end\x18\a \x01(\x05H\x01R\x0fallowedHoursEnd\x88\x01\x01\x12A\n" +
	"\x0especific_dates\x18\b \x03(\v2\x1a.google.protobuf.TimestampR\rspecificDates\x12Q\n" +
	"\x12recurrence_pattern\x18\t \x01(\x0e2\x1d.voucher.v1.RecurrencePatternH\x02R\x11recurrencePattern\x88\x01\x01\x12:\n" +
	"\x17recurrence_day_of_month\x18\n" +
	" \x01(\x05H\x03R\x14recurrenceDayOfMonth\x88\x01\x01\x12.\n" +
	"\x10recurrence_month\x18\v \x01(\x05H\x04R\x0frecurrenceMonth\x88\x01\x01\x128\n" +
	"\x16recurrence_day_of_week\x18\f \x01(\x05H\x05R\x13recurrenceDayOfWeek\x88\x01\x01B\x16\n" +
	"\x14_allowed_hours_startB\x14\n" +
	"\x12_allowed_hours_endB\x15\n" +
	"\x13_recurrence_patternB\x1a\n" +
	"\x18_recurrence_day_of_monthB\x13\n" +
	"\x11_recurrence_monthB\x19\n" +
	"\x17_recurrence_day_of_week\"\x94\x04\n" +
	"\x16VoucherUserEligibility\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x129\n" +
	"\n" +
	"created_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x03 \x01(\tR\tvoucherId\x12\x1c\n" +
	"\auser_id\x18\x04 \x01(\tH\x00R\x06userId\x88\x01\x01\x12 \n" +
	"\tuser_type\x18\x05 \x01(\tH\x01R\buserType\x88\x01\x01\x124\n" +
	"\x14min_account_age_days\x18\x06 \x01(\x05H\x02R\x11minAccountAgeDays\x88\x01\x01\x124\n" +
	"\x14max_account_age_days\x18\a \x01(\x05H\x03R\x11maxAccountAgeDays\x88\x01\x01\x123\n" +
	"\x13min_previous_orders\x18\b \x01(\x03H\x04R\x11minPreviousOrders\x88\x01\x01\x123\n" +
	"\x13max_previous_orders\x18\t \x01(\x03H\x05R\x11maxPreviousOrders\x88\x01\x01B\n" +
	"\n" +
	"\b_user_idB\f\n" +
	"\n" +
	"_user_typeB\x17\n" +
	"\x15_min_account_age_daysB\x17\n" +
	"\x15_max_account_age_daysB\x16\n" +
	"\x14_min_previous_ordersB\x16\n" +
	"\x14_max_previous_orders\"\x9e\x01\n" +
	"\x11VoucherOrderUsage\x12\x19\n" +
	"\border_id\x18\x01 \x01(\tR\aorderId\x123\n" +
	"\aused_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x06usedAt\x12!\n" +
	"\forder_amount\x18\x03 \x01(\x01R\vorderAmount\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\"\xca\x01\n" +
	"\x10UserVoucherUsage\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x1f\n" +
	"\vusage_count\x18\x02 \x01(\x05R\n" +
	"usageCount\x12\x1b\n" +
	"\tfull_name\x18\x03 \x01(\tR\bfullName\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x12\n" +
	"\x04type\x18\x05 \x01(\tR\x04type\x125\n" +
	"\x06orders\x18\x06 \x03(\v2\x1d.voucher.v1.VoucherOrderUsageR\x06orders\"\x88\v\n" +
	"\aVoucher\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12!\n" +
	"\fvoucher_code\x18\x02 \x01(\tR\vvoucherCode\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12(\n" +
	"\x10discount_type_id\x18\x05 \x01(\tR\x0ediscountTypeId\x12%\n" +
	"\x0ediscount_value\x18\x06 \x01(\x01R\rdiscountValue\x12:\n" +
	"\fusage_method\x18\a \x01(\x0e2\x17.voucher.v1.UsageMethodR\vusageMethod\x129\n" +
	"\n" +
	"valid_from\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tvalidFrom\x12;\n" +
	"\vvalid_until\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"validUntil\x12+\n" +
	"\x0fmax_usage_count\x18\n" +
	" \x01(\x05H\x00R\rmaxUsageCount\x88\x01\x01\x120\n" +
	"\x12max_usage_per_user\x18\v \x01(\x05H\x01R\x0fmaxUsagePerUser\x88\x01\x01\x122\n" +
	"\x15user_eligibility_type\x18\f \x01(\tR\x13userEligibilityType\x12.\n" +
	"\x13current_usage_count\x18\r \x01(\x05R\x11currentUsageCount\x12(\n" +
	"\x10min_order_amount\x18\x0e \x01(\x01R\x0eminOrderAmount\x123\n" +
	"\x13max_discount_amount\x18\x0f \x01(\x01H\x02R\x11maxDiscountAmount\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"created_by\x18\x10 \x01(\tR\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x12 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12B\n" +
	"\rdiscount_type\x18\x13 \x01(\v2\x18.voucher.v1.DiscountTypeH\x03R\fdiscountType\x88\x01\x01\x121\n" +
	"\x06status\x18\x14 \x01(\x0e2\x19.voucher.v1.VoucherStatusR\x06status\x12X\n" +
	"\x14product_restrictions\x18\x15 \x03(\v2%.voucher.v1.VoucherProductRestrictionR\x13productRestrictions\x12O\n" +
	"\x11time_restrictions\x18\x16 \x03(\v2\".voucher.v1.VoucherTimeRestrictionR\x10timeRestrictions\x12X\n" +
	"\x16user_eligibility_rules\x18\x17 \x03(\v2\".voucher.v1.VoucherUserEligibilityR\x14userEligibilityRules\x12=\n" +
	"\vuser_usages\x18\x18 \x03(\v2\x1c.voucher.v1.UserVoucherUsageR\n" +
	"userUsages\x12#\n" +
	"\rtotal_savings\x18\x19 \x01(\x01R\ftotalSavings\x12!\n" +
	"\funique_users\x18\x1a \x01(\x05R\vuniqueUsersB\x12\n" +
	"\x10_max_usage_countB\x15\n" +
	"\x13_max_usage_per_userB\x16\n" +
	"\x14_max_discount_amountB\x10\n" +
	"\x0e_discount_type\"\xaf\x05\n" +
	"\x14CreateVoucherRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12!\n" +
	"\fvoucher_code\x18\x02 \x01(\tR\vvoucherCode\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12(\n" +
	"\x10discount_type_id\x18\x05 \x01(\tR\x0ediscountTypeId\x12%\n" +
	"\x0ediscount_value\x18\x06 \x01(\x01R\rdiscountValue\x12:\n" +
	"\fusage_method\x18\a \x01(\x0e2\x17.voucher.v1.UsageMethodR\vusageMethod\x129\n" +
	"\n" +
	"valid_from\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tvalidFrom\x12;\n" +
	"\vvalid_until\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"validUntil\x12+\n" +
	"\x0fmax_usage_count\x18\n" +
	" \x01(\x05H\x00R\rmaxUsageCount\x88\x01\x01\x120\n" +
	"\x12max_usage_per_user\x18\v \x01(\x05H\x01R\x0fmaxUsagePerUser\x88\x01\x01\x12(\n" +
	"\x10min_order_amount\x18\f \x01(\x01R\x0eminOrderAmount\x123\n" +
	"\x13max_discount_amount\x18\r \x01(\x01H\x02R\x11maxDiscountAmount\x88\x01\x01B\x12\n" +
	"\x10_max_usage_countB\x15\n" +
	"\x13_max_usage_per_userB\x16\n" +
	"\x14_max_discount_amount\"\xae\x01\n" +
	"\x15CreateVoucherResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12-\n" +
	"\avoucher\x18\x02 \x01(\v2\x13.voucher.v1.VoucherR\avoucher\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"j\n" +
	"\x11GetVoucherRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x02 \x01(\tR\tvoucherId\"\xab\x01\n" +
	"\x12GetVoucherResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12-\n" +
	"\avoucher\x18\x02 \x01(\v2\x13.voucher.v1.VoucherR\avoucher\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"t\n" +
	"\x17GetVoucherByCodeRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12!\n" +
	"\fvoucher_code\x18\x02 \x01(\tR\vvoucherCode\"\xb1\x01\n" +
	"\x18GetVoucherByCodeResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12-\n" +
	"\avoucher\x18\x02 \x01(\v2\x13.voucher.v1.VoucherR\avoucher\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xdc\a\n" +
	"\x14UpdateVoucherRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x02 \x01(\tR\tvoucherId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12(\n" +
	"\x10discount_type_id\x18\x05 \x01(\tR\x0ediscountTypeId\x12%\n" +
	"\x0ediscount_value\x18\x06 \x01(\x01R\rdiscountValue\x12:\n" +
	"\fusage_method\x18\a \x01(\x0e2\x17.voucher.v1.UsageMethodR\vusageMethod\x121\n" +
	"\x06status\x18\b \x01(\x0e2\x19.voucher.v1.VoucherStatusR\x06status\x12(\n" +
	"\x10min_order_amount\x18\t \x01(\x01R\x0eminOrderAmount\x123\n" +
	"\x13max_discount_amount\x18\n" +
	" \x01(\x01H\x00R\x11maxDiscountAmount\x88\x01\x01\x12+\n" +
	"\x0fmax_usage_count\x18\v \x01(\x05H\x01R\rmaxUsageCount\x88\x01\x01\x120\n" +
	"\x12max_usage_per_user\x18\f \x01(\x05H\x02R\x0fmaxUsagePerUser\x88\x01\x01\x129\n" +
	"\n" +
	"valid_from\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tvalidFrom\x12;\n" +
	"\vvalid_until\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"validUntil\x12X\n" +
	"\x14product_restrictions\x18\x0f \x03(\v2%.voucher.v1.VoucherProductRestrictionR\x13productRestrictions\x12O\n" +
	"\x11time_restrictions\x18\x10 \x03(\v2\".voucher.v1.VoucherTimeRestrictionR\x10timeRestrictions\x12Q\n" +
	"\x12user_eligibilities\x18\x11 \x03(\v2\".voucher.v1.VoucherUserEligibilityR\x11userEligibilitiesB\x16\n" +
	"\x14_max_discount_amountB\x12\n" +
	"\x10_max_usage_countB\x15\n" +
	"\x13_max_usage_per_user\"\xae\x01\n" +
	"\x15UpdateVoucherResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12-\n" +
	"\avoucher\x18\x02 \x01(\v2\x13.voucher.v1.VoucherR\avoucher\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xf5\x02\n" +
	"\x13ListVouchersRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06search\x18\x04 \x01(\tR\x06search\x12-\n" +
	"\x10discount_type_id\x18\x05 \x01(\tH\x00R\x0ediscountTypeId\x88\x01\x01\x12?\n" +
	"\fusage_method\x18\x06 \x01(\x0e2\x17.voucher.v1.UsageMethodH\x01R\vusageMethod\x88\x01\x01\x12\x16\n" +
	"\x06status\x18\a \x01(\tR\x06status\x12\x17\n" +
	"\asort_by\x18\b \x01(\tR\x06sortBy\x12\x1d\n" +
	"\n" +
	"sort_order\x18\t \x01(\tR\tsortOrderB\x13\n" +
	"\x11_discount_type_idB\x0f\n" +
	"\r_usage_method\"\x90\x02\n" +
	"\x14ListVouchersResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12/\n" +
	"\bvouchers\x18\x02 \x03(\v2\x13.voucher.v1.VoucherR\bvouchers\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x05R\x04page\x12\x14\n" +
	"\x05limit\x18\x05 \x01(\x05R\x05limit\x12\x1f\n" +
	"\vtotal_pages\x18\x06 \x01(\x05R\n" +
	"totalPages\x12-\n" +
	"\x05error\x18\a \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"Q\n" +
	"\x17GetDiscountTypesRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\"\xc3\x01\n" +
	"\x18GetDiscountTypesResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12?\n" +
	"\x0ediscount_types\x18\x02 \x03(\v2\x18.voucher.v1.DiscountTypeR\rdiscountTypes\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xb1\x02\n" +
	"\x1eCheckVoucherEligibilityRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12!\n" +
	"\fvoucher_code\x18\x02 \x01(\tR\vvoucherCode\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12!\n" +
	"\forder_amount\x18\x04 \x01(\x01R\vorderAmount\x12C\n" +
	"\x0forder_timestamp\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x0eorderTimestamp\x123\n" +
	"\n" +
	"cart_items\x18\x06 \x03(\v2\x14.voucher.v1.CartItemR\tcartItems\"\x87\x02\n" +
	"\x1fCheckVoucherEligibilityResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x1a\n" +
	"\beligible\x18\x02 \x01(\bR\beligible\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x04 \x01(\tR\tvoucherId\x12'\n" +
	"\x0fdiscount_amount\x18\x05 \x01(\x01R\x0ediscountAmount\x12-\n" +
	"\x05error\x18\x06 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\x89\x01\n" +
	"\x13EligibleVoucherInfo\x12\x1a\n" +
	"\beligible\x18\x01 \x01(\bR\beligible\x12-\n" +
	"\avoucher\x18\x02 \x01(\v2\x13.voucher.v1.VoucherR\avoucher\x12'\n" +
	"\x0fdiscount_amount\x18\x03 \x01(\x01R\x0ediscountAmount\"\x8f\x02\n" +
	"\x1fListAutoEligibleVouchersRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12!\n" +
	"\forder_amount\x18\x03 \x01(\x01R\vorderAmount\x12C\n" +
	"\x0forder_timestamp\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0eorderTimestamp\x123\n" +
	"\n" +
	"cart_items\x18\x05 \x03(\v2\x14.voucher.v1.CartItemR\tcartItems\"\xc7\x01\n" +
	" ListAutoEligibleVouchersResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12;\n" +
	"\bvouchers\x18\x02 \x03(\v2\x1f.voucher.v1.EligibleVoucherInfoR\bvouchers\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error*[\n" +
	"\vUsageMethod\x12\x1c\n" +
	"\x18USAGE_METHOD_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13USAGE_METHOD_MANUAL\x10\x01\x12\x15\n" +
	"\x11USAGE_METHOD_AUTO\x10\x02*\x83\x01\n" +
	"\rVoucherStatus\x12\x1e\n" +
	"\x1aVOUCHER_STATUS_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15VOUCHER_STATUS_ACTIVE\x10\x01\x12\x1b\n" +
	"\x17VOUCHER_STATUS_INACTIVE\x10\x02\x12\x1a\n" +
	"\x16VOUCHER_STATUS_EXPIRED\x10\x03*\xe1\x01\n" +
	"\x13TimeRestrictionType\x12%\n" +
	"!TIME_RESTRICTION_TYPE_UNSPECIFIED\x10\x00\x12&\n" +
	"\"TIME_RESTRICTION_TYPE_DAYS_OF_WEEK\x10\x01\x12&\n" +
	"\"TIME_RESTRICTION_TYPE_HOURS_OF_DAY\x10\x02\x12(\n" +
	"$TIME_RESTRICTION_TYPE_SPECIFIC_DATES\x10\x03\x12)\n" +
	"%TIME_RESTRICTION_TYPE_RECURRING_DATES\x10\x04*\xd5\x01\n" +
	"\x11RecurrencePattern\x12\"\n" +
	"\x1eRECURRENCE_PATTERN_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18RECURRENCE_PATTERN_DAILY\x10\x01\x12\x1d\n" +
	"\x19RECURRENCE_PATTERN_WEEKLY\x10\x02\x12\x1e\n" +
	"\x1aRECURRENCE_PATTERN_MONTHLY\x10\x03\x12 \n" +
	"\x1cRECURRENCE_PATTERN_QUARTERLY\x10\x04\x12\x1d\n" +
	"\x19RECURRENCE_PATTERN_YEARLY\x10\x052\xd3\x06\n" +
	"\x0eVoucherService\x12T\n" +
	"\rCreateVoucher\x12 .voucher.v1.CreateVoucherRequest\x1a!.voucher.v1.CreateVoucherResponse\x12K\n" +
	"\n" +
	"GetVoucher\x12\x1d.voucher.v1.GetVoucherRequest\x1a\x1e.voucher.v1.GetVoucherResponse\x12]\n" +
	"\x10GetVoucherByCode\x12#.voucher.v1.GetVoucherByCodeRequest\x1a$.voucher.v1.GetVoucherByCodeResponse\x12T\n" +
	"\rUpdateVoucher\x12 .voucher.v1.UpdateVoucherRequest\x1a!.voucher.v1.UpdateVoucherResponse\x12Q\n" +
	"\fListVouchers\x12\x1f.voucher.v1.ListVouchersRequest\x1a .voucher.v1.ListVouchersResponse\x12r\n" +
	"\x17CheckVoucherEligibility\x12*.voucher.v1.CheckVoucherEligibilityRequest\x1a+.voucher.v1.CheckVoucherEligibilityResponse\x12u\n" +
	"\x18ListAutoEligibleVouchers\x12+.voucher.v1.ListAutoEligibleVouchersRequest\x1a,.voucher.v1.ListAutoEligibleVouchersResponse\x12]\n" +
	"\x10GetDiscountTypes\x12#.voucher.v1.GetDiscountTypesRequest\x1a$.voucher.v1.GetDiscountTypesResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB9Z7gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1b\x06proto3"

var (
	file_voucher_v1_voucher_service_proto_rawDescOnce sync.Once
	file_voucher_v1_voucher_service_proto_rawDescData []byte
)

func file_voucher_v1_voucher_service_proto_rawDescGZIP() []byte {
	file_voucher_v1_voucher_service_proto_rawDescOnce.Do(func() {
		file_voucher_v1_voucher_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_voucher_v1_voucher_service_proto_rawDesc), len(file_voucher_v1_voucher_service_proto_rawDesc)))
	})
	return file_voucher_v1_voucher_service_proto_rawDescData
}

var file_voucher_v1_voucher_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_voucher_v1_voucher_service_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_voucher_v1_voucher_service_proto_goTypes = []any{
	(UsageMethod)(0),                         // 0: voucher.v1.UsageMethod
	(VoucherStatus)(0),                       // 1: voucher.v1.VoucherStatus
	(TimeRestrictionType)(0),                 // 2: voucher.v1.TimeRestrictionType
	(RecurrencePattern)(0),                   // 3: voucher.v1.RecurrencePattern
	(*DiscountType)(nil),                     // 4: voucher.v1.DiscountType
	(*CartItem)(nil),                         // 5: voucher.v1.CartItem
	(*VoucherProductRestriction)(nil),        // 6: voucher.v1.VoucherProductRestriction
	(*VoucherTimeRestriction)(nil),           // 7: voucher.v1.VoucherTimeRestriction
	(*VoucherUserEligibility)(nil),           // 8: voucher.v1.VoucherUserEligibility
	(*VoucherOrderUsage)(nil),                // 9: voucher.v1.VoucherOrderUsage
	(*UserVoucherUsage)(nil),                 // 10: voucher.v1.UserVoucherUsage
	(*Voucher)(nil),                          // 11: voucher.v1.Voucher
	(*CreateVoucherRequest)(nil),             // 12: voucher.v1.CreateVoucherRequest
	(*CreateVoucherResponse)(nil),            // 13: voucher.v1.CreateVoucherResponse
	(*GetVoucherRequest)(nil),                // 14: voucher.v1.GetVoucherRequest
	(*GetVoucherResponse)(nil),               // 15: voucher.v1.GetVoucherResponse
	(*GetVoucherByCodeRequest)(nil),          // 16: voucher.v1.GetVoucherByCodeRequest
	(*GetVoucherByCodeResponse)(nil),         // 17: voucher.v1.GetVoucherByCodeResponse
	(*UpdateVoucherRequest)(nil),             // 18: voucher.v1.UpdateVoucherRequest
	(*UpdateVoucherResponse)(nil),            // 19: voucher.v1.UpdateVoucherResponse
	(*ListVouchersRequest)(nil),              // 20: voucher.v1.ListVouchersRequest
	(*ListVouchersResponse)(nil),             // 21: voucher.v1.ListVouchersResponse
	(*GetDiscountTypesRequest)(nil),          // 22: voucher.v1.GetDiscountTypesRequest
	(*GetDiscountTypesResponse)(nil),         // 23: voucher.v1.GetDiscountTypesResponse
	(*CheckVoucherEligibilityRequest)(nil),   // 24: voucher.v1.CheckVoucherEligibilityRequest
	(*CheckVoucherEligibilityResponse)(nil),  // 25: voucher.v1.CheckVoucherEligibilityResponse
	(*EligibleVoucherInfo)(nil),              // 26: voucher.v1.EligibleVoucherInfo
	(*ListAutoEligibleVouchersRequest)(nil),  // 27: voucher.v1.ListAutoEligibleVouchersRequest
	(*ListAutoEligibleVouchersResponse)(nil), // 28: voucher.v1.ListAutoEligibleVouchersResponse
	(*timestamppb.Timestamp)(nil),            // 29: google.protobuf.Timestamp
	(*v1.RequestMetadata)(nil),               // 30: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),              // 31: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),                  // 32: common.v1.ServiceError
	(*v1.HealthCheckRequest)(nil),            // 33: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),           // 34: common.v1.HealthCheckResponse
}
var file_voucher_v1_voucher_service_proto_depIdxs = []int32{
	29, // 0: voucher.v1.DiscountType.created_at:type_name -> google.protobuf.Timestamp
	29, // 1: voucher.v1.DiscountType.updated_at:type_name -> google.protobuf.Timestamp
	29, // 2: voucher.v1.VoucherProductRestriction.created_at:type_name -> google.protobuf.Timestamp
	29, // 3: voucher.v1.VoucherTimeRestriction.created_at:type_name -> google.protobuf.Timestamp
	2,  // 4: voucher.v1.VoucherTimeRestriction.restriction_type:type_name -> voucher.v1.TimeRestrictionType
	29, // 5: voucher.v1.VoucherTimeRestriction.specific_dates:type_name -> google.protobuf.Timestamp
	3,  // 6: voucher.v1.VoucherTimeRestriction.recurrence_pattern:type_name -> voucher.v1.RecurrencePattern
	29, // 7: voucher.v1.VoucherUserEligibility.created_at:type_name -> google.protobuf.Timestamp
	29, // 8: voucher.v1.VoucherOrderUsage.used_at:type_name -> google.protobuf.Timestamp
	9,  // 9: voucher.v1.UserVoucherUsage.orders:type_name -> voucher.v1.VoucherOrderUsage
	0,  // 10: voucher.v1.Voucher.usage_method:type_name -> voucher.v1.UsageMethod
	29, // 11: voucher.v1.Voucher.valid_from:type_name -> google.protobuf.Timestamp
	29, // 12: voucher.v1.Voucher.valid_until:type_name -> google.protobuf.Timestamp
	29, // 13: voucher.v1.Voucher.created_at:type_name -> google.protobuf.Timestamp
	29, // 14: voucher.v1.Voucher.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 15: voucher.v1.Voucher.discount_type:type_name -> voucher.v1.DiscountType
	1,  // 16: voucher.v1.Voucher.status:type_name -> voucher.v1.VoucherStatus
	6,  // 17: voucher.v1.Voucher.product_restrictions:type_name -> voucher.v1.VoucherProductRestriction
	7,  // 18: voucher.v1.Voucher.time_restrictions:type_name -> voucher.v1.VoucherTimeRestriction
	8,  // 19: voucher.v1.Voucher.user_eligibility_rules:type_name -> voucher.v1.VoucherUserEligibility
	10, // 20: voucher.v1.Voucher.user_usages:type_name -> voucher.v1.UserVoucherUsage
	30, // 21: voucher.v1.CreateVoucherRequest.metadata:type_name -> common.v1.RequestMetadata
	0,  // 22: voucher.v1.CreateVoucherRequest.usage_method:type_name -> voucher.v1.UsageMethod
	29, // 23: voucher.v1.CreateVoucherRequest.valid_from:type_name -> google.protobuf.Timestamp
	29, // 24: voucher.v1.CreateVoucherRequest.valid_until:type_name -> google.protobuf.Timestamp
	31, // 25: voucher.v1.CreateVoucherResponse.metadata:type_name -> common.v1.ResponseMetadata
	11, // 26: voucher.v1.CreateVoucherResponse.voucher:type_name -> voucher.v1.Voucher
	32, // 27: voucher.v1.CreateVoucherResponse.error:type_name -> common.v1.ServiceError
	30, // 28: voucher.v1.GetVoucherRequest.metadata:type_name -> common.v1.RequestMetadata
	31, // 29: voucher.v1.GetVoucherResponse.metadata:type_name -> common.v1.ResponseMetadata
	11, // 30: voucher.v1.GetVoucherResponse.voucher:type_name -> voucher.v1.Voucher
	32, // 31: voucher.v1.GetVoucherResponse.error:type_name -> common.v1.ServiceError
	30, // 32: voucher.v1.GetVoucherByCodeRequest.metadata:type_name -> common.v1.RequestMetadata
	31, // 33: voucher.v1.GetVoucherByCodeResponse.metadata:type_name -> common.v1.ResponseMetadata
	11, // 34: voucher.v1.GetVoucherByCodeResponse.voucher:type_name -> voucher.v1.Voucher
	32, // 35: voucher.v1.GetVoucherByCodeResponse.error:type_name -> common.v1.ServiceError
	30, // 36: voucher.v1.UpdateVoucherRequest.metadata:type_name -> common.v1.RequestMetadata
	0,  // 37: voucher.v1.UpdateVoucherRequest.usage_method:type_name -> voucher.v1.UsageMethod
	1,  // 38: voucher.v1.UpdateVoucherRequest.status:type_name -> voucher.v1.VoucherStatus
	29, // 39: voucher.v1.UpdateVoucherRequest.valid_from:type_name -> google.protobuf.Timestamp
	29, // 40: voucher.v1.UpdateVoucherRequest.valid_until:type_name -> google.protobuf.Timestamp
	6,  // 41: voucher.v1.UpdateVoucherRequest.product_restrictions:type_name -> voucher.v1.VoucherProductRestriction
	7,  // 42: voucher.v1.UpdateVoucherRequest.time_restrictions:type_name -> voucher.v1.VoucherTimeRestriction
	8,  // 43: voucher.v1.UpdateVoucherRequest.user_eligibilities:type_name -> voucher.v1.VoucherUserEligibility
	31, // 44: voucher.v1.UpdateVoucherResponse.metadata:type_name -> common.v1.ResponseMetadata
	11, // 45: voucher.v1.UpdateVoucherResponse.voucher:type_name -> voucher.v1.Voucher
	32, // 46: voucher.v1.UpdateVoucherResponse.error:type_name -> common.v1.ServiceError
	30, // 47: voucher.v1.ListVouchersRequest.metadata:type_name -> common.v1.RequestMetadata
	0,  // 48: voucher.v1.ListVouchersRequest.usage_method:type_name -> voucher.v1.UsageMethod
	31, // 49: voucher.v1.ListVouchersResponse.metadata:type_name -> common.v1.ResponseMetadata
	11, // 50: voucher.v1.ListVouchersResponse.vouchers:type_name -> voucher.v1.Voucher
	32, // 51: voucher.v1.ListVouchersResponse.error:type_name -> common.v1.ServiceError
	30, // 52: voucher.v1.GetDiscountTypesRequest.metadata:type_name -> common.v1.RequestMetadata
	31, // 53: voucher.v1.GetDiscountTypesResponse.metadata:type_name -> common.v1.ResponseMetadata
	4,  // 54: voucher.v1.GetDiscountTypesResponse.discount_types:type_name -> voucher.v1.DiscountType
	32, // 55: voucher.v1.GetDiscountTypesResponse.error:type_name -> common.v1.ServiceError
	30, // 56: voucher.v1.CheckVoucherEligibilityRequest.metadata:type_name -> common.v1.RequestMetadata
	29, // 57: voucher.v1.CheckVoucherEligibilityRequest.order_timestamp:type_name -> google.protobuf.Timestamp
	5,  // 58: voucher.v1.CheckVoucherEligibilityRequest.cart_items:type_name -> voucher.v1.CartItem
	31, // 59: voucher.v1.CheckVoucherEligibilityResponse.metadata:type_name -> common.v1.ResponseMetadata
	32, // 60: voucher.v1.CheckVoucherEligibilityResponse.error:type_name -> common.v1.ServiceError
	11, // 61: voucher.v1.EligibleVoucherInfo.voucher:type_name -> voucher.v1.Voucher
	30, // 62: voucher.v1.ListAutoEligibleVouchersRequest.metadata:type_name -> common.v1.RequestMetadata
	29, // 63: voucher.v1.ListAutoEligibleVouchersRequest.order_timestamp:type_name -> google.protobuf.Timestamp
	5,  // 64: voucher.v1.ListAutoEligibleVouchersRequest.cart_items:type_name -> voucher.v1.CartItem
	31, // 65: voucher.v1.ListAutoEligibleVouchersResponse.metadata:type_name -> common.v1.ResponseMetadata
	26, // 66: voucher.v1.ListAutoEligibleVouchersResponse.vouchers:type_name -> voucher.v1.EligibleVoucherInfo
	32, // 67: voucher.v1.ListAutoEligibleVouchersResponse.error:type_name -> common.v1.ServiceError
	12, // 68: voucher.v1.VoucherService.CreateVoucher:input_type -> voucher.v1.CreateVoucherRequest
	14, // 69: voucher.v1.VoucherService.GetVoucher:input_type -> voucher.v1.GetVoucherRequest
	16, // 70: voucher.v1.VoucherService.GetVoucherByCode:input_type -> voucher.v1.GetVoucherByCodeRequest
	18, // 71: voucher.v1.VoucherService.UpdateVoucher:input_type -> voucher.v1.UpdateVoucherRequest
	20, // 72: voucher.v1.VoucherService.ListVouchers:input_type -> voucher.v1.ListVouchersRequest
	24, // 73: voucher.v1.VoucherService.CheckVoucherEligibility:input_type -> voucher.v1.CheckVoucherEligibilityRequest
	27, // 74: voucher.v1.VoucherService.ListAutoEligibleVouchers:input_type -> voucher.v1.ListAutoEligibleVouchersRequest
	22, // 75: voucher.v1.VoucherService.GetDiscountTypes:input_type -> voucher.v1.GetDiscountTypesRequest
	33, // 76: voucher.v1.VoucherService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	13, // 77: voucher.v1.VoucherService.CreateVoucher:output_type -> voucher.v1.CreateVoucherResponse
	15, // 78: voucher.v1.VoucherService.GetVoucher:output_type -> voucher.v1.GetVoucherResponse
	17, // 79: voucher.v1.VoucherService.GetVoucherByCode:output_type -> voucher.v1.GetVoucherByCodeResponse
	19, // 80: voucher.v1.VoucherService.UpdateVoucher:output_type -> voucher.v1.UpdateVoucherResponse
	21, // 81: voucher.v1.VoucherService.ListVouchers:output_type -> voucher.v1.ListVouchersResponse
	25, // 82: voucher.v1.VoucherService.CheckVoucherEligibility:output_type -> voucher.v1.CheckVoucherEligibilityResponse
	28, // 83: voucher.v1.VoucherService.ListAutoEligibleVouchers:output_type -> voucher.v1.ListAutoEligibleVouchersResponse
	23, // 84: voucher.v1.VoucherService.GetDiscountTypes:output_type -> voucher.v1.GetDiscountTypesResponse
	34, // 85: voucher.v1.VoucherService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	77, // [77:86] is the sub-list for method output_type
	68, // [68:77] is the sub-list for method input_type
	68, // [68:68] is the sub-list for extension type_name
	68, // [68:68] is the sub-list for extension extendee
	0,  // [0:68] is the sub-list for field type_name
}

func init() { file_voucher_v1_voucher_service_proto_init() }
func file_voucher_v1_voucher_service_proto_init() {
	if File_voucher_v1_voucher_service_proto != nil {
		return
	}
	file_voucher_v1_voucher_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_voucher_v1_voucher_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_voucher_v1_voucher_service_proto_msgTypes[3].OneofWrappers = []any{}
	file_voucher_v1_voucher_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_voucher_v1_voucher_service_proto_msgTypes[7].OneofWrappers = []any{}
	file_voucher_v1_voucher_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_voucher_v1_voucher_service_proto_msgTypes[14].OneofWrappers = []any{}
	file_voucher_v1_voucher_service_proto_msgTypes[16].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_voucher_v1_voucher_service_proto_rawDesc), len(file_voucher_v1_voucher_service_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_voucher_v1_voucher_service_proto_goTypes,
		DependencyIndexes: file_voucher_v1_voucher_service_proto_depIdxs,
		EnumInfos:         file_voucher_v1_voucher_service_proto_enumTypes,
		MessageInfos:      file_voucher_v1_voucher_service_proto_msgTypes,
	}.Build()
	File_voucher_v1_voucher_service_proto = out.File
	file_voucher_v1_voucher_service_proto_goTypes = nil
	file_voucher_v1_voucher_service_proto_depIdxs = nil
}
