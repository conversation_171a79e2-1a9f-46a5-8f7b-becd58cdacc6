// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: order/v1/order_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OrderItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProductId     string                 `protobuf:"bytes,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Quantity      int32                  `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Price         float64                `protobuf:"fixed64,3,opt,name=price,proto3" json:"price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderItem) Reset() {
	*x = OrderItem{}
	mi := &file_order_v1_order_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderItem) ProtoMessage() {}

func (x *OrderItem) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderItem.ProtoReflect.Descriptor instead.
func (*OrderItem) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{0}
}

func (x *OrderItem) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *OrderItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderItem) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type Order struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId           string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Items            []*OrderItem           `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	TotalAmount      float64                `protobuf:"fixed64,4,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	Status           string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	AppliedVoucherId *string                `protobuf:"bytes,8,opt,name=applied_voucher_id,json=appliedVoucherId,proto3,oneof" json:"applied_voucher_id,omitempty"`
	DiscountAmount   *float64               `protobuf:"fixed64,9,opt,name=discount_amount,json=discountAmount,proto3,oneof" json:"discount_amount,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Order) Reset() {
	*x = Order{}
	mi := &file_order_v1_order_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{1}
}

func (x *Order) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Order) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Order) GetItems() []*OrderItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Order) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *Order) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Order) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Order) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Order) GetAppliedVoucherId() string {
	if x != nil && x.AppliedVoucherId != nil {
		return *x.AppliedVoucherId
	}
	return ""
}

func (x *Order) GetDiscountAmount() float64 {
	if x != nil && x.DiscountAmount != nil {
		return *x.DiscountAmount
	}
	return 0
}

type CreateOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Items         []*OrderItem           `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	mi := &file_order_v1_order_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateOrderRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateOrderRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateOrderRequest) GetItems() []*OrderItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type CreateOrderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Order         *Order                 `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrderResponse) Reset() {
	*x = CreateOrderResponse{}
	mi := &file_order_v1_order_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResponse) ProtoMessage() {}

func (x *CreateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateOrderResponse) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateOrderResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateOrderResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *CreateOrderResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	OrderId       string                 `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderRequest) Reset() {
	*x = GetOrderRequest{}
	mi := &file_order_v1_order_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderRequest) ProtoMessage() {}

func (x *GetOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderRequest.ProtoReflect.Descriptor instead.
func (*GetOrderRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetOrderRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetOrderRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type GetOrderResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Order         *Order                 `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderResponse) Reset() {
	*x = GetOrderResponse{}
	mi := &file_order_v1_order_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderResponse) ProtoMessage() {}

func (x *GetOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderResponse.ProtoReflect.Descriptor instead.
func (*GetOrderResponse) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetOrderResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetOrderResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *GetOrderResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type UpdateOrderStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	OrderId       string                 `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOrderStatusRequest) Reset() {
	*x = UpdateOrderStatusRequest{}
	mi := &file_order_v1_order_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOrderStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderStatusRequest) ProtoMessage() {}

func (x *UpdateOrderStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateOrderStatusRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateOrderStatusRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateOrderStatusRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *UpdateOrderStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type UpdateOrderStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Order         *Order                 `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateOrderStatusResponse) Reset() {
	*x = UpdateOrderStatusResponse{}
	mi := &file_order_v1_order_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateOrderStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateOrderStatusResponse) ProtoMessage() {}

func (x *UpdateOrderStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateOrderStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateOrderStatusResponse) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateOrderStatusResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateOrderStatusResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *UpdateOrderStatusResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ListOrdersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersRequest) Reset() {
	*x = ListOrdersRequest{}
	mi := &file_order_v1_order_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersRequest) ProtoMessage() {}

func (x *ListOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersRequest.ProtoReflect.Descriptor instead.
func (*ListOrdersRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListOrdersRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListOrdersRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ListOrdersRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type ListOrdersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Orders        []*Order               `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
	Pagination    *v1.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListOrdersResponse) Reset() {
	*x = ListOrdersResponse{}
	mi := &file_order_v1_order_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOrdersResponse) ProtoMessage() {}

func (x *ListOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOrdersResponse.ProtoReflect.Descriptor instead.
func (*ListOrdersResponse) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListOrdersResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListOrdersResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListOrdersResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListOrdersResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetUserOrderCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserOrderCountRequest) Reset() {
	*x = GetUserOrderCountRequest{}
	mi := &file_order_v1_order_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserOrderCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserOrderCountRequest) ProtoMessage() {}

func (x *GetUserOrderCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserOrderCountRequest.ProtoReflect.Descriptor instead.
func (*GetUserOrderCountRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetUserOrderCountRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserOrderCountRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserOrderCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	OrderCount    int64                  `protobuf:"varint,2,opt,name=order_count,json=orderCount,proto3" json:"order_count,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserOrderCountResponse) Reset() {
	*x = GetUserOrderCountResponse{}
	mi := &file_order_v1_order_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserOrderCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserOrderCountResponse) ProtoMessage() {}

func (x *GetUserOrderCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserOrderCountResponse.ProtoReflect.Descriptor instead.
func (*GetUserOrderCountResponse) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserOrderCountResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserOrderCountResponse) GetOrderCount() int64 {
	if x != nil {
		return x.OrderCount
	}
	return 0
}

func (x *GetUserOrderCountResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type GetUserVoucherUsageCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	VoucherId     string                 `protobuf:"bytes,3,opt,name=voucher_id,json=voucherId,proto3" json:"voucher_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserVoucherUsageCountRequest) Reset() {
	*x = GetUserVoucherUsageCountRequest{}
	mi := &file_order_v1_order_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserVoucherUsageCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVoucherUsageCountRequest) ProtoMessage() {}

func (x *GetUserVoucherUsageCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVoucherUsageCountRequest.ProtoReflect.Descriptor instead.
func (*GetUserVoucherUsageCountRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetUserVoucherUsageCountRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserVoucherUsageCountRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserVoucherUsageCountRequest) GetVoucherId() string {
	if x != nil {
		return x.VoucherId
	}
	return ""
}

type GetUserVoucherUsageCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UsageCount    int32                  `protobuf:"varint,2,opt,name=usage_count,json=usageCount,proto3" json:"usage_count,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserVoucherUsageCountResponse) Reset() {
	*x = GetUserVoucherUsageCountResponse{}
	mi := &file_order_v1_order_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserVoucherUsageCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVoucherUsageCountResponse) ProtoMessage() {}

func (x *GetUserVoucherUsageCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVoucherUsageCountResponse.ProtoReflect.Descriptor instead.
func (*GetUserVoucherUsageCountResponse) Descriptor() ([]byte, []int) {
	return file_order_v1_order_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetUserVoucherUsageCountResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserVoucherUsageCountResponse) GetUsageCount() int32 {
	if x != nil {
		return x.UsageCount
	}
	return 0
}

func (x *GetUserVoucherUsageCountResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_order_v1_order_service_proto protoreflect.FileDescriptor

const file_order_v1_order_service_proto_rawDesc = "" +
	"\n" +
	"\x1corder/v1/order_service.proto\x12\border.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1acommon/v1/pagination.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\\\n" +
	"\tOrderItem\x12\x1d\n" +
	"\n" +
	"product_id\x18\x01 \x01(\tR\tproductId\x12\x1a\n" +
	"\bquantity\x18\x02 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x01R\x05price\"\x98\x03\n" +
	"\x05Order\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12)\n" +
	"\x05items\x18\x03 \x03(\v2\x13.order.v1.OrderItemR\x05items\x12!\n" +
	"\ftotal_amount\x18\x04 \x01(\x01R\vtotalAmount\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x121\n" +
	"\x12applied_voucher_id\x18\b \x01(\tH\x00R\x10appliedVoucherId\x88\x01\x01\x12,\n" +
	"\x0fdiscount_amount\x18\t \x01(\x01H\x01R\x0ediscountAmount\x88\x01\x01B\x15\n" +
	"\x13_applied_voucher_idB\x12\n" +
	"\x10_discount_amount\"\x90\x01\n" +
	"\x12CreateOrderRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12)\n" +
	"\x05items\x18\x03 \x03(\v2\x13.order.v1.OrderItemR\x05items\"\xa4\x01\n" +
	"\x13CreateOrderResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12%\n" +
	"\x05order\x18\x02 \x01(\v2\x0f.order.v1.OrderR\x05order\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"d\n" +
	"\x0fGetOrderRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x19\n" +
	"\border_id\x18\x02 \x01(\tR\aorderId\"\xa1\x01\n" +
	"\x10GetOrderResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12%\n" +
	"\x05order\x18\x02 \x01(\v2\x0f.order.v1.OrderR\x05order\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\x85\x01\n" +
	"\x18UpdateOrderStatusRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x19\n" +
	"\border_id\x18\x02 \x01(\tR\aorderId\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\"\xaa\x01\n" +
	"\x19UpdateOrderStatusResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12%\n" +
	"\x05order\x18\x02 \x01(\v2\x0f.order.v1.OrderR\x05order\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xa2\x01\n" +
	"\x11ListOrdersRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.common.v1.PaginationRequestR\n" +
	"pagination\"\xe4\x01\n" +
	"\x12ListOrdersResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12'\n" +
	"\x06orders\x18\x02 \x03(\v2\x0f.order.v1.OrderR\x06orders\x12=\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1d.common.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x04 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"k\n" +
	"\x18GetUserOrderCountRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"\xa4\x01\n" +
	"\x19GetUserOrderCountResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x1f\n" +
	"\vorder_count\x18\x02 \x01(\x03R\n" +
	"orderCount\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\x91\x01\n" +
	"\x1fGetUserVoucherUsageCountRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"voucher_id\x18\x03 \x01(\tR\tvoucherId\"\xab\x01\n" +
	" GetUserVoucherUsageCountResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x1f\n" +
	"\vusage_count\x18\x02 \x01(\x05R\n" +
	"usageCount\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error2\xe3\x04\n" +
	"\fOrderService\x12J\n" +
	"\vCreateOrder\x12\x1c.order.v1.CreateOrderRequest\x1a\x1d.order.v1.CreateOrderResponse\x12A\n" +
	"\bGetOrder\x12\x19.order.v1.GetOrderRequest\x1a\x1a.order.v1.GetOrderResponse\x12\\\n" +
	"\x11UpdateOrderStatus\x12\".order.v1.UpdateOrderStatusRequest\x1a#.order.v1.UpdateOrderStatusResponse\x12G\n" +
	"\n" +
	"ListOrders\x12\x1b.order.v1.ListOrdersRequest\x1a\x1c.order.v1.ListOrdersResponse\x12\\\n" +
	"\x11GetUserOrderCount\x12\".order.v1.GetUserOrderCountRequest\x1a#.order.v1.GetUserOrderCountResponse\x12q\n" +
	"\x18GetUserVoucherUsageCount\x12).order.v1.GetUserVoucherUsageCountRequest\x1a*.order.v1.GetUserVoucherUsageCountResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB7Z5gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/order/v1b\x06proto3"

var (
	file_order_v1_order_service_proto_rawDescOnce sync.Once
	file_order_v1_order_service_proto_rawDescData []byte
)

func file_order_v1_order_service_proto_rawDescGZIP() []byte {
	file_order_v1_order_service_proto_rawDescOnce.Do(func() {
		file_order_v1_order_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_order_v1_order_service_proto_rawDesc), len(file_order_v1_order_service_proto_rawDesc)))
	})
	return file_order_v1_order_service_proto_rawDescData
}

var file_order_v1_order_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_order_v1_order_service_proto_goTypes = []any{
	(*OrderItem)(nil),                        // 0: order.v1.OrderItem
	(*Order)(nil),                            // 1: order.v1.Order
	(*CreateOrderRequest)(nil),               // 2: order.v1.CreateOrderRequest
	(*CreateOrderResponse)(nil),              // 3: order.v1.CreateOrderResponse
	(*GetOrderRequest)(nil),                  // 4: order.v1.GetOrderRequest
	(*GetOrderResponse)(nil),                 // 5: order.v1.GetOrderResponse
	(*UpdateOrderStatusRequest)(nil),         // 6: order.v1.UpdateOrderStatusRequest
	(*UpdateOrderStatusResponse)(nil),        // 7: order.v1.UpdateOrderStatusResponse
	(*ListOrdersRequest)(nil),                // 8: order.v1.ListOrdersRequest
	(*ListOrdersResponse)(nil),               // 9: order.v1.ListOrdersResponse
	(*GetUserOrderCountRequest)(nil),         // 10: order.v1.GetUserOrderCountRequest
	(*GetUserOrderCountResponse)(nil),        // 11: order.v1.GetUserOrderCountResponse
	(*GetUserVoucherUsageCountRequest)(nil),  // 12: order.v1.GetUserVoucherUsageCountRequest
	(*GetUserVoucherUsageCountResponse)(nil), // 13: order.v1.GetUserVoucherUsageCountResponse
	(*timestamppb.Timestamp)(nil),            // 14: google.protobuf.Timestamp
	(*v1.RequestMetadata)(nil),               // 15: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),              // 16: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),                  // 17: common.v1.ServiceError
	(*v1.PaginationRequest)(nil),             // 18: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),            // 19: common.v1.PaginationResponse
	(*v1.HealthCheckRequest)(nil),            // 20: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),           // 21: common.v1.HealthCheckResponse
}
var file_order_v1_order_service_proto_depIdxs = []int32{
	0,  // 0: order.v1.Order.items:type_name -> order.v1.OrderItem
	14, // 1: order.v1.Order.created_at:type_name -> google.protobuf.Timestamp
	14, // 2: order.v1.Order.updated_at:type_name -> google.protobuf.Timestamp
	15, // 3: order.v1.CreateOrderRequest.metadata:type_name -> common.v1.RequestMetadata
	0,  // 4: order.v1.CreateOrderRequest.items:type_name -> order.v1.OrderItem
	16, // 5: order.v1.CreateOrderResponse.metadata:type_name -> common.v1.ResponseMetadata
	1,  // 6: order.v1.CreateOrderResponse.order:type_name -> order.v1.Order
	17, // 7: order.v1.CreateOrderResponse.error:type_name -> common.v1.ServiceError
	15, // 8: order.v1.GetOrderRequest.metadata:type_name -> common.v1.RequestMetadata
	16, // 9: order.v1.GetOrderResponse.metadata:type_name -> common.v1.ResponseMetadata
	1,  // 10: order.v1.GetOrderResponse.order:type_name -> order.v1.Order
	17, // 11: order.v1.GetOrderResponse.error:type_name -> common.v1.ServiceError
	15, // 12: order.v1.UpdateOrderStatusRequest.metadata:type_name -> common.v1.RequestMetadata
	16, // 13: order.v1.UpdateOrderStatusResponse.metadata:type_name -> common.v1.ResponseMetadata
	1,  // 14: order.v1.UpdateOrderStatusResponse.order:type_name -> order.v1.Order
	17, // 15: order.v1.UpdateOrderStatusResponse.error:type_name -> common.v1.ServiceError
	15, // 16: order.v1.ListOrdersRequest.metadata:type_name -> common.v1.RequestMetadata
	18, // 17: order.v1.ListOrdersRequest.pagination:type_name -> common.v1.PaginationRequest
	16, // 18: order.v1.ListOrdersResponse.metadata:type_name -> common.v1.ResponseMetadata
	1,  // 19: order.v1.ListOrdersResponse.orders:type_name -> order.v1.Order
	19, // 20: order.v1.ListOrdersResponse.pagination:type_name -> common.v1.PaginationResponse
	17, // 21: order.v1.ListOrdersResponse.error:type_name -> common.v1.ServiceError
	15, // 22: order.v1.GetUserOrderCountRequest.metadata:type_name -> common.v1.RequestMetadata
	16, // 23: order.v1.GetUserOrderCountResponse.metadata:type_name -> common.v1.ResponseMetadata
	17, // 24: order.v1.GetUserOrderCountResponse.error:type_name -> common.v1.ServiceError
	15, // 25: order.v1.GetUserVoucherUsageCountRequest.metadata:type_name -> common.v1.RequestMetadata
	16, // 26: order.v1.GetUserVoucherUsageCountResponse.metadata:type_name -> common.v1.ResponseMetadata
	17, // 27: order.v1.GetUserVoucherUsageCountResponse.error:type_name -> common.v1.ServiceError
	2,  // 28: order.v1.OrderService.CreateOrder:input_type -> order.v1.CreateOrderRequest
	4,  // 29: order.v1.OrderService.GetOrder:input_type -> order.v1.GetOrderRequest
	6,  // 30: order.v1.OrderService.UpdateOrderStatus:input_type -> order.v1.UpdateOrderStatusRequest
	8,  // 31: order.v1.OrderService.ListOrders:input_type -> order.v1.ListOrdersRequest
	10, // 32: order.v1.OrderService.GetUserOrderCount:input_type -> order.v1.GetUserOrderCountRequest
	12, // 33: order.v1.OrderService.GetUserVoucherUsageCount:input_type -> order.v1.GetUserVoucherUsageCountRequest
	20, // 34: order.v1.OrderService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	3,  // 35: order.v1.OrderService.CreateOrder:output_type -> order.v1.CreateOrderResponse
	5,  // 36: order.v1.OrderService.GetOrder:output_type -> order.v1.GetOrderResponse
	7,  // 37: order.v1.OrderService.UpdateOrderStatus:output_type -> order.v1.UpdateOrderStatusResponse
	9,  // 38: order.v1.OrderService.ListOrders:output_type -> order.v1.ListOrdersResponse
	11, // 39: order.v1.OrderService.GetUserOrderCount:output_type -> order.v1.GetUserOrderCountResponse
	13, // 40: order.v1.OrderService.GetUserVoucherUsageCount:output_type -> order.v1.GetUserVoucherUsageCountResponse
	21, // 41: order.v1.OrderService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	35, // [35:42] is the sub-list for method output_type
	28, // [28:35] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_order_v1_order_service_proto_init() }
func file_order_v1_order_service_proto_init() {
	if File_order_v1_order_service_proto != nil {
		return
	}
	file_order_v1_order_service_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_order_v1_order_service_proto_rawDesc), len(file_order_v1_order_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_order_v1_order_service_proto_goTypes,
		DependencyIndexes: file_order_v1_order_service_proto_depIdxs,
		MessageInfos:      file_order_v1_order_service_proto_msgTypes,
	}.Build()
	File_order_v1_order_service_proto = out.File
	file_order_v1_order_service_proto_goTypes = nil
	file_order_v1_order_service_proto_depIdxs = nil
}
