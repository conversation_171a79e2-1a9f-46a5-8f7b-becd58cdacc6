// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: product/v1/product_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProductStatus int32

const (
	ProductStatus_PRODUCT_STATUS_UNSPECIFIED ProductStatus = 0
	ProductStatus_PRODUCT_STATUS_ACTIVE      ProductStatus = 1
	ProductStatus_PRODUCT_STATUS_INACTIVE    ProductStatus = 2
)

// Enum value maps for ProductStatus.
var (
	ProductStatus_name = map[int32]string{
		0: "PRODUCT_STATUS_UNSPECIFIED",
		1: "PRODUCT_STATUS_ACTIVE",
		2: "PRODUCT_STATUS_INACTIVE",
	}
	ProductStatus_value = map[string]int32{
		"PRODUCT_STATUS_UNSPECIFIED": 0,
		"PRODUCT_STATUS_ACTIVE":      1,
		"PRODUCT_STATUS_INACTIVE":    2,
	}
)

func (x ProductStatus) Enum() *ProductStatus {
	p := new(ProductStatus)
	*p = x
	return p
}

func (x ProductStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProductStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_product_v1_product_service_proto_enumTypes[0].Descriptor()
}

func (ProductStatus) Type() protoreflect.EnumType {
	return &file_product_v1_product_service_proto_enumTypes[0]
}

func (x ProductStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProductStatus.Descriptor instead.
func (ProductStatus) EnumDescriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{0}
}

type Category struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Category) Reset() {
	*x = Category{}
	mi := &file_product_v1_product_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Category) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Category) ProtoMessage() {}

func (x *Category) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Category.ProtoReflect.Descriptor instead.
func (*Category) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{0}
}

func (x *Category) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Category) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Category) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Category) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type Product struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Price         float64                `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	CategoryId    string                 `protobuf:"bytes,5,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ImageUrl      string                 `protobuf:"bytes,6,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	StockQuantity int64                  `protobuf:"varint,7,opt,name=stock_quantity,json=stockQuantity,proto3" json:"stock_quantity,omitempty"`
	Status        ProductStatus          `protobuf:"varint,8,opt,name=status,proto3,enum=product.v1.ProductStatus" json:"status,omitempty"`
	Brand         string                 `protobuf:"bytes,9,opt,name=brand,proto3" json:"brand,omitempty"`
	Sku           string                 `protobuf:"bytes,10,opt,name=sku,proto3" json:"sku,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Category      *Category              `protobuf:"bytes,13,opt,name=category,proto3" json:"category,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Product) Reset() {
	*x = Product{}
	mi := &file_product_v1_product_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Product) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Product) ProtoMessage() {}

func (x *Product) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Product.ProtoReflect.Descriptor instead.
func (*Product) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{1}
}

func (x *Product) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Product) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Product) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Product) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Product) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *Product) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *Product) GetStockQuantity() int64 {
	if x != nil {
		return x.StockQuantity
	}
	return 0
}

func (x *Product) GetStatus() ProductStatus {
	if x != nil {
		return x.Status
	}
	return ProductStatus_PRODUCT_STATUS_UNSPECIFIED
}

func (x *Product) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *Product) GetSku() string {
	if x != nil {
		return x.Sku
	}
	return ""
}

func (x *Product) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Product) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Product) GetCategory() *Category {
	if x != nil {
		return x.Category
	}
	return nil
}

type GetProductRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ProductId     string                 `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProductRequest) Reset() {
	*x = GetProductRequest{}
	mi := &file_product_v1_product_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProductRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductRequest) ProtoMessage() {}

func (x *GetProductRequest) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductRequest.ProtoReflect.Descriptor instead.
func (*GetProductRequest) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetProductRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetProductRequest) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

type GetProductResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Product       *Product               `protobuf:"bytes,2,opt,name=product,proto3" json:"product,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProductResponse) Reset() {
	*x = GetProductResponse{}
	mi := &file_product_v1_product_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProductResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductResponse) ProtoMessage() {}

func (x *GetProductResponse) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductResponse.ProtoReflect.Descriptor instead.
func (*GetProductResponse) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetProductResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetProductResponse) GetProduct() *Product {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *GetProductResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type UpdateProductRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ProductId     string                 `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Price         float64                `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	CategoryId    string                 `protobuf:"bytes,6,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ImageUrl      string                 `protobuf:"bytes,7,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	StockQuantity int64                  `protobuf:"varint,8,opt,name=stock_quantity,json=stockQuantity,proto3" json:"stock_quantity,omitempty"`
	Status        ProductStatus          `protobuf:"varint,9,opt,name=status,proto3,enum=product.v1.ProductStatus" json:"status,omitempty"`
	Brand         string                 `protobuf:"bytes,10,opt,name=brand,proto3" json:"brand,omitempty"`
	Sku           string                 `protobuf:"bytes,11,opt,name=sku,proto3" json:"sku,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProductRequest) Reset() {
	*x = UpdateProductRequest{}
	mi := &file_product_v1_product_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProductRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProductRequest) ProtoMessage() {}

func (x *UpdateProductRequest) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProductRequest.ProtoReflect.Descriptor instead.
func (*UpdateProductRequest) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateProductRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateProductRequest) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *UpdateProductRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateProductRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateProductRequest) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *UpdateProductRequest) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *UpdateProductRequest) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *UpdateProductRequest) GetStockQuantity() int64 {
	if x != nil {
		return x.StockQuantity
	}
	return 0
}

func (x *UpdateProductRequest) GetStatus() ProductStatus {
	if x != nil {
		return x.Status
	}
	return ProductStatus_PRODUCT_STATUS_UNSPECIFIED
}

func (x *UpdateProductRequest) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *UpdateProductRequest) GetSku() string {
	if x != nil {
		return x.Sku
	}
	return ""
}

type UpdateProductResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Product       *Product               `protobuf:"bytes,2,opt,name=product,proto3" json:"product,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProductResponse) Reset() {
	*x = UpdateProductResponse{}
	mi := &file_product_v1_product_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProductResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProductResponse) ProtoMessage() {}

func (x *UpdateProductResponse) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProductResponse.ProtoReflect.Descriptor instead.
func (*UpdateProductResponse) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateProductResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *UpdateProductResponse) GetProduct() *Product {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *UpdateProductResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ListProductsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Search        string                 `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	CategoryId    string                 `protobuf:"bytes,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Status        ProductStatus          `protobuf:"varint,5,opt,name=status,proto3,enum=product.v1.ProductStatus" json:"status,omitempty"`
	SortBy        string                 `protobuf:"bytes,6,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty"`
	SortOrder     string                 `protobuf:"bytes,7,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListProductsRequest) Reset() {
	*x = ListProductsRequest{}
	mi := &file_product_v1_product_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListProductsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductsRequest) ProtoMessage() {}

func (x *ListProductsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductsRequest.ProtoReflect.Descriptor instead.
func (*ListProductsRequest) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListProductsRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListProductsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListProductsRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *ListProductsRequest) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *ListProductsRequest) GetStatus() ProductStatus {
	if x != nil {
		return x.Status
	}
	return ProductStatus_PRODUCT_STATUS_UNSPECIFIED
}

func (x *ListProductsRequest) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *ListProductsRequest) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

type ListProductsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Products      []*Product             `protobuf:"bytes,2,rep,name=products,proto3" json:"products,omitempty"`
	Pagination    *v1.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListProductsResponse) Reset() {
	*x = ListProductsResponse{}
	mi := &file_product_v1_product_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListProductsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListProductsResponse) ProtoMessage() {}

func (x *ListProductsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListProductsResponse.ProtoReflect.Descriptor instead.
func (*ListProductsResponse) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{7}
}

func (x *ListProductsResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListProductsResponse) GetProducts() []*Product {
	if x != nil {
		return x.Products
	}
	return nil
}

func (x *ListProductsResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListProductsResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ListCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Search        string                 `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCategoriesRequest) Reset() {
	*x = ListCategoriesRequest{}
	mi := &file_product_v1_product_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCategoriesRequest) ProtoMessage() {}

func (x *ListCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCategoriesRequest.ProtoReflect.Descriptor instead.
func (*ListCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{8}
}

func (x *ListCategoriesRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListCategoriesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListCategoriesRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

type ListCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Categories    []*Category            `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
	Pagination    *v1.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCategoriesResponse) Reset() {
	*x = ListCategoriesResponse{}
	mi := &file_product_v1_product_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCategoriesResponse) ProtoMessage() {}

func (x *ListCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_product_v1_product_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCategoriesResponse.ProtoReflect.Descriptor instead.
func (*ListCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_product_v1_product_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListCategoriesResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListCategoriesResponse) GetCategories() []*Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *ListCategoriesResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListCategoriesResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_product_v1_product_service_proto protoreflect.FileDescriptor

const file_product_v1_product_service_proto_rawDesc = "" +
	"\n" +
	" product/v1/product_service.proto\x12\n" +
	"product.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1acommon/v1/pagination.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8b\x01\n" +
	"\bCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"\xcd\x03\n" +
	"\aProduct\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x01R\x05price\x12\x1f\n" +
	"\vcategory_id\x18\x05 \x01(\tR\n" +
	"categoryId\x12\x1b\n" +
	"\timage_url\x18\x06 \x01(\tR\bimageUrl\x12%\n" +
	"\x0estock_quantity\x18\a \x01(\x03R\rstockQuantity\x121\n" +
	"\x06status\x18\b \x01(\x0e2\x19.product.v1.ProductStatusR\x06status\x12\x14\n" +
	"\x05brand\x18\t \x01(\tR\x05brand\x12\x10\n" +
	"\x03sku\x18\n" +
	" \x01(\tR\x03sku\x129\n" +
	"\n" +
	"created_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x120\n" +
	"\bcategory\x18\r \x01(\v2\x14.product.v1.CategoryR\bcategory\"j\n" +
	"\x11GetProductRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"product_id\x18\x02 \x01(\tR\tproductId\"\xab\x01\n" +
	"\x12GetProductResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12-\n" +
	"\aproduct\x18\x02 \x01(\v2\x13.product.v1.ProductR\aproduct\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xf9\x02\n" +
	"\x14UpdateProductRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"product_id\x18\x02 \x01(\tR\tproductId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x14\n" +
	"\x05price\x18\x05 \x01(\x01R\x05price\x12\x1f\n" +
	"\vcategory_id\x18\x06 \x01(\tR\n" +
	"categoryId\x12\x1b\n" +
	"\timage_url\x18\a \x01(\tR\bimageUrl\x12%\n" +
	"\x0estock_quantity\x18\b \x01(\x03R\rstockQuantity\x121\n" +
	"\x06status\x18\t \x01(\x0e2\x19.product.v1.ProductStatusR\x06status\x12\x14\n" +
	"\x05brand\x18\n" +
	" \x01(\tR\x05brand\x12\x10\n" +
	"\x03sku\x18\v \x01(\tR\x03sku\"\xae\x01\n" +
	"\x15UpdateProductResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12-\n" +
	"\aproduct\x18\x02 \x01(\v2\x13.product.v1.ProductR\aproduct\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xaf\x02\n" +
	"\x13ListProductsRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.common.v1.PaginationRequestR\n" +
	"pagination\x12\x16\n" +
	"\x06search\x18\x03 \x01(\tR\x06search\x12\x1f\n" +
	"\vcategory_id\x18\x04 \x01(\tR\n" +
	"categoryId\x121\n" +
	"\x06status\x18\x05 \x01(\x0e2\x19.product.v1.ProductStatusR\x06status\x12\x17\n" +
	"\asort_by\x18\x06 \x01(\tR\x06sortBy\x12\x1d\n" +
	"\n" +
	"sort_order\x18\a \x01(\tR\tsortOrder\"\xee\x01\n" +
	"\x14ListProductsResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12/\n" +
	"\bproducts\x18\x02 \x03(\v2\x13.product.v1.ProductR\bproducts\x12=\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1d.common.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x04 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xa5\x01\n" +
	"\x15ListCategoriesRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.common.v1.PaginationRequestR\n" +
	"pagination\x12\x16\n" +
	"\x06search\x18\x03 \x01(\tR\x06search\"\xf5\x01\n" +
	"\x16ListCategoriesResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x124\n" +
	"\n" +
	"categories\x18\x02 \x03(\v2\x14.product.v1.CategoryR\n" +
	"categories\x12=\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1d.common.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x04 \x01(\v2\x17.common.v1.ServiceErrorR\x05error*g\n" +
	"\rProductStatus\x12\x1e\n" +
	"\x1aPRODUCT_STATUS_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15PRODUCT_STATUS_ACTIVE\x10\x01\x12\x1b\n" +
	"\x17PRODUCT_STATUS_INACTIVE\x10\x022\xad\x03\n" +
	"\x0eProductService\x12K\n" +
	"\n" +
	"GetProduct\x12\x1d.product.v1.GetProductRequest\x1a\x1e.product.v1.GetProductResponse\x12T\n" +
	"\rUpdateProduct\x12 .product.v1.UpdateProductRequest\x1a!.product.v1.UpdateProductResponse\x12Q\n" +
	"\fListProducts\x12\x1f.product.v1.ListProductsRequest\x1a .product.v1.ListProductsResponse\x12W\n" +
	"\x0eListCategories\x12!.product.v1.ListCategoriesRequest\x1a\".product.v1.ListCategoriesResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB9Z7gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1b\x06proto3"

var (
	file_product_v1_product_service_proto_rawDescOnce sync.Once
	file_product_v1_product_service_proto_rawDescData []byte
)

func file_product_v1_product_service_proto_rawDescGZIP() []byte {
	file_product_v1_product_service_proto_rawDescOnce.Do(func() {
		file_product_v1_product_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_product_v1_product_service_proto_rawDesc), len(file_product_v1_product_service_proto_rawDesc)))
	})
	return file_product_v1_product_service_proto_rawDescData
}

var file_product_v1_product_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_product_v1_product_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_product_v1_product_service_proto_goTypes = []any{
	(ProductStatus)(0),             // 0: product.v1.ProductStatus
	(*Category)(nil),               // 1: product.v1.Category
	(*Product)(nil),                // 2: product.v1.Product
	(*GetProductRequest)(nil),      // 3: product.v1.GetProductRequest
	(*GetProductResponse)(nil),     // 4: product.v1.GetProductResponse
	(*UpdateProductRequest)(nil),   // 5: product.v1.UpdateProductRequest
	(*UpdateProductResponse)(nil),  // 6: product.v1.UpdateProductResponse
	(*ListProductsRequest)(nil),    // 7: product.v1.ListProductsRequest
	(*ListProductsResponse)(nil),   // 8: product.v1.ListProductsResponse
	(*ListCategoriesRequest)(nil),  // 9: product.v1.ListCategoriesRequest
	(*ListCategoriesResponse)(nil), // 10: product.v1.ListCategoriesResponse
	(*timestamppb.Timestamp)(nil),  // 11: google.protobuf.Timestamp
	(*v1.RequestMetadata)(nil),     // 12: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),    // 13: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),        // 14: common.v1.ServiceError
	(*v1.PaginationRequest)(nil),   // 15: common.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),  // 16: common.v1.PaginationResponse
	(*v1.HealthCheckRequest)(nil),  // 17: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil), // 18: common.v1.HealthCheckResponse
}
var file_product_v1_product_service_proto_depIdxs = []int32{
	11, // 0: product.v1.Category.created_at:type_name -> google.protobuf.Timestamp
	0,  // 1: product.v1.Product.status:type_name -> product.v1.ProductStatus
	11, // 2: product.v1.Product.created_at:type_name -> google.protobuf.Timestamp
	11, // 3: product.v1.Product.updated_at:type_name -> google.protobuf.Timestamp
	1,  // 4: product.v1.Product.category:type_name -> product.v1.Category
	12, // 5: product.v1.GetProductRequest.metadata:type_name -> common.v1.RequestMetadata
	13, // 6: product.v1.GetProductResponse.metadata:type_name -> common.v1.ResponseMetadata
	2,  // 7: product.v1.GetProductResponse.product:type_name -> product.v1.Product
	14, // 8: product.v1.GetProductResponse.error:type_name -> common.v1.ServiceError
	12, // 9: product.v1.UpdateProductRequest.metadata:type_name -> common.v1.RequestMetadata
	0,  // 10: product.v1.UpdateProductRequest.status:type_name -> product.v1.ProductStatus
	13, // 11: product.v1.UpdateProductResponse.metadata:type_name -> common.v1.ResponseMetadata
	2,  // 12: product.v1.UpdateProductResponse.product:type_name -> product.v1.Product
	14, // 13: product.v1.UpdateProductResponse.error:type_name -> common.v1.ServiceError
	12, // 14: product.v1.ListProductsRequest.metadata:type_name -> common.v1.RequestMetadata
	15, // 15: product.v1.ListProductsRequest.pagination:type_name -> common.v1.PaginationRequest
	0,  // 16: product.v1.ListProductsRequest.status:type_name -> product.v1.ProductStatus
	13, // 17: product.v1.ListProductsResponse.metadata:type_name -> common.v1.ResponseMetadata
	2,  // 18: product.v1.ListProductsResponse.products:type_name -> product.v1.Product
	16, // 19: product.v1.ListProductsResponse.pagination:type_name -> common.v1.PaginationResponse
	14, // 20: product.v1.ListProductsResponse.error:type_name -> common.v1.ServiceError
	12, // 21: product.v1.ListCategoriesRequest.metadata:type_name -> common.v1.RequestMetadata
	15, // 22: product.v1.ListCategoriesRequest.pagination:type_name -> common.v1.PaginationRequest
	13, // 23: product.v1.ListCategoriesResponse.metadata:type_name -> common.v1.ResponseMetadata
	1,  // 24: product.v1.ListCategoriesResponse.categories:type_name -> product.v1.Category
	16, // 25: product.v1.ListCategoriesResponse.pagination:type_name -> common.v1.PaginationResponse
	14, // 26: product.v1.ListCategoriesResponse.error:type_name -> common.v1.ServiceError
	3,  // 27: product.v1.ProductService.GetProduct:input_type -> product.v1.GetProductRequest
	5,  // 28: product.v1.ProductService.UpdateProduct:input_type -> product.v1.UpdateProductRequest
	7,  // 29: product.v1.ProductService.ListProducts:input_type -> product.v1.ListProductsRequest
	9,  // 30: product.v1.ProductService.ListCategories:input_type -> product.v1.ListCategoriesRequest
	17, // 31: product.v1.ProductService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	4,  // 32: product.v1.ProductService.GetProduct:output_type -> product.v1.GetProductResponse
	6,  // 33: product.v1.ProductService.UpdateProduct:output_type -> product.v1.UpdateProductResponse
	8,  // 34: product.v1.ProductService.ListProducts:output_type -> product.v1.ListProductsResponse
	10, // 35: product.v1.ProductService.ListCategories:output_type -> product.v1.ListCategoriesResponse
	18, // 36: product.v1.ProductService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	32, // [32:37] is the sub-list for method output_type
	27, // [27:32] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_product_v1_product_service_proto_init() }
func file_product_v1_product_service_proto_init() {
	if File_product_v1_product_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_product_v1_product_service_proto_rawDesc), len(file_product_v1_product_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_product_v1_product_service_proto_goTypes,
		DependencyIndexes: file_product_v1_product_service_proto_depIdxs,
		EnumInfos:         file_product_v1_product_service_proto_enumTypes,
		MessageInfos:      file_product_v1_product_service_proto_msgTypes,
	}.Build()
	File_product_v1_product_service_proto = out.File
	file_product_v1_product_service_proto_goTypes = nil
	file_product_v1_product_service_proto_depIdxs = nil
}
