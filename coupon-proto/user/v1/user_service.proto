syntax = "proto3";

package user.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1";

service UserService {
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc GetUserByEmail(GetUserByEmailRequest) returns (GetUserByEmailResponse);
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc HealthCheck(common.v1.HealthCheckRequest) 
    returns (common.v1.HealthCheckResponse);
}

enum UserType {
  USER_TYPE_UNSPECIFIED = 0;
  USER_TYPE_NEW = 1;
  USER_TYPE_VIP = 2;
}

message User {
  string id = 1;
  string email = 2;
  string name = 3;
  string password = 4;
  string role = 5;
  UserType type = 6;
  google.protobuf.Timestamp created_at = 7;
}

message LoginRequest {
  common.v1.RequestMetadata metadata = 1;
  string email = 2;
  string password = 3;
}

message LoginResponse {
  common.v1.ResponseMetadata metadata = 1;
  User user = 2;
  common.v1.ServiceError error = 3;
}

message GetUserRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
}

message GetUserResponse {
  common.v1.ResponseMetadata metadata = 1;
  User user = 2;
  common.v1.ServiceError error = 3;
}

message GetUserByEmailRequest {
  common.v1.RequestMetadata metadata = 1;
  string email = 2;
}

message GetUserByEmailResponse {
  common.v1.ResponseMetadata metadata = 1;
  User user = 2;
  common.v1.ServiceError error = 3;
}

message CreateUserRequest {
  common.v1.RequestMetadata metadata = 1;
  string name = 2;
  string email = 3;
  string password = 4;
}

message CreateUserResponse {
  common.v1.ResponseMetadata metadata = 1;
  User user = 2;
  common.v1.ServiceError error = 3;
}
