syntax = "proto3";

package auth.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1";

service AuthService {
  rpc RegisterService(RegisterServiceRequest) returns (RegisterServiceResponse);
  rpc ValidateServiceCredentials(ValidateServiceCredentialsRequest)
    returns (ValidateServiceCredentialsResponse);
  rpc HealthCheck(common.v1.HealthCheckRequest)
    returns (common.v1.HealthCheckResponse);
}

message RegisterServiceRequest {
  common.v1.RequestMetadata metadata = 1;
  string service_name = 2;
  string service_version = 3;
  string description = 4;
}

message RegisterServiceResponse {
  common.v1.ResponseMetadata metadata = 1;
  string service_id = 2;
  string client_id = 3;
  string client_key = 4;
  common.v1.ServiceError error = 5;
}

message ValidateServiceCredentialsRequest {
  common.v1.RequestMetadata metadata = 1;
  string client_id = 2;
  string client_key = 3;
  string service_name = 4;
}

message ValidateServiceCredentialsResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool valid = 2;
  string service_id = 3;
  string service_name = 4;
  common.v1.ServiceError error = 5;
}
