package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type Producer struct {
	writer *kafka.Writer
	config *config.KafkaConfig
	logger *logging.Logger
}

type Message struct {
	Key       string            `json:"key"`
	Value     any               `json:"value"`
	Headers   map[string]string `json:"headers"`
	Timestamp time.Time         `json:"timestamp"`
}

func NewProducer(cfg *config.KafkaConfig, logger *logging.Logger) *Producer {
	writer := &kafka.Writer{
		Addr:     kafka.TCP(cfg.Brokers...),
		Balancer: &kafka.LeastBytes{},
	}

	return &Producer{
		writer: writer,
		config: cfg,
		logger: logger,
	}
}

func (p *Producer) SendMessage(ctx context.Context, topic string, msg *Message) error {
	valueBytes, err := json.Marshal(msg.Value)
	if err != nil {
		return fmt.Errorf("failed to marshal message value: %w", err)
	}

	headers := make([]kafka.Header, 0, len(msg.Headers))
	for k, v := range msg.Headers {
		headers = append(headers, kafka.Header{
			Key:   k,
			Value: []byte(v),
		})
	}

	kafkaMsg := kafka.Message{
		Topic:   topic,
		Key:     []byte(msg.Key),
		Value:   valueBytes,
		Headers: headers,
		Time:    msg.Timestamp,
	}

	start := time.Now()
	err = p.writer.WriteMessages(ctx, kafkaMsg)
	duration := time.Since(start)

	fields := logrus.Fields{
		"topic":    topic,
		"key":      msg.Key,
		"duration": duration.String(),
	}

	if err != nil {
		fields["error"] = err.Error()
		p.logger.WithContext(ctx).WithFields(fields).Error("Failed to send Kafka message")
		return fmt.Errorf("failed to send message to topic %s: %w", topic, err)
	}

	p.logger.WithContext(ctx).WithFields(fields).Debug("Kafka message sent successfully")
	return nil
}

func (p *Producer) Close() error {
	return p.writer.Close()
}
