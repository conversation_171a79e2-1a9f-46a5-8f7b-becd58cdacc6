package auth

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"google.golang.org/grpc/metadata"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
)

type JWTManager struct {
	secret     string
	expiration time.Duration
}

type Claims struct {
	UserID   string `json:"user_id"`
	Email    string `json:"email"`
	Role     string `json:"role"`
	ClientID string `json:"client_id"`
	jwt.RegisteredClaims
}

type contextKey string

const (
	userIDKey   contextKey = "user_id"
	emailKey    contextKey = "email"
	roleKey     contextKey = "role"
	clientIDKey contextKey = "client_id"
)

func NewJWTManager(cfg *config.AuthConfig) *JWTManager {
	return &JWTManager{
		secret:     cfg.JWTSecret,
		expiration: cfg.JWTExpiration,
	}
}

func (j *JWTManager) GenerateToken(userID, email, role string) (string, error) {
	claims := &Claims{
		UserID: userID,
		Email:  email,
		Role:   role,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Subject:   userID,
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.expiration)),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secret))
}

func (j *JWTManager) GenerateServiceToken(clientID string) (string, error) {
	claims := &Claims{
		ClientID: clientID,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        uuid.New().String(),
			Subject:   clientID,
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.expiration)),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(j.secret))
}

func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (any, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(j.secret), nil
	})
	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

func (j *JWTManager) ExtractTokenFromContext(ctx context.Context) (string, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "", errors.New("missing metadata")
	}

	authorization := md.Get("authorization")
	if len(authorization) == 0 {
		return "", errors.New("missing authorization header")
	}

	token := authorization[0]
	if !strings.HasPrefix(token, "Bearer ") {
		return "", errors.New("invalid authorization header format")
	}

	return strings.TrimPrefix(token, "Bearer "), nil
}

func (j *JWTManager) ExtractTokenFromCookie(r *http.Request, cookieName string) (string, error) {
	cookie, err := r.Cookie(cookieName)
	if err != nil {
		return "", errors.New("missing authentication cookie")
	}

	if cookie.Value == "" {
		return "", errors.New("empty authentication cookie")
	}

	return cookie.Value, nil
}

func (j *JWTManager) AuthFunc(ctx context.Context) (context.Context, error) {
	token, err := j.ExtractTokenFromContext(ctx)
	if err != nil {
		return nil, err
	}

	claims, err := j.ValidateToken(token)
	if err != nil {
		return nil, err
	}

	ctx = context.WithValue(ctx, userIDKey, claims.UserID)
	ctx = context.WithValue(ctx, emailKey, claims.Email)
	ctx = context.WithValue(ctx, roleKey, claims.Role)
	ctx = context.WithValue(ctx, clientIDKey, claims.ClientID)

	return ctx, nil
}

func GetUserIDFromContext(ctx context.Context) (string, bool) {
	userID, ok := ctx.Value(userIDKey).(string)
	return userID, ok
}

func GetEmailFromContext(ctx context.Context) (string, bool) {
	email, ok := ctx.Value(emailKey).(string)
	return email, ok
}

func GetRoleFromContext(ctx context.Context) (string, bool) {
	role, ok := ctx.Value(roleKey).(string)
	return role, ok
}

func GetClientIDFromContext(ctx context.Context) (string, bool) {
	clientID, ok := ctx.Value(clientIDKey).(string)
	return clientID, ok
}
