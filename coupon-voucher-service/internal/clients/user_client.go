package clients

import (
	"context"
	"fmt"
	"time"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type UserClient struct {
	client proto_user_v1.UserServiceClient
	conn   *shared_grpc.Client
}

func NewUserClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*UserClient, error) {
	client, err := shared_grpc.NewAuthenticatedClient(target, cfg, logger, metrics, clientID, clientKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create authenticated gRPC client for user service: %w", err)
	}

	return &UserClient{
		client: proto_user_v1.NewUserServiceClient(client.Conn),
		conn:   client,
	}, nil
}

func (c *UserClient) Close() {
	c.conn.Close()
}

func (c *UserClient) GetUser(ctx context.Context, userID string) (*proto_user_v1.User, error) {
	req := &proto_user_v1.GetUserRequest{UserId: userID}
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.GetUser(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.User, nil
}
