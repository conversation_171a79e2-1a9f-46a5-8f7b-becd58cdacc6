package service

import (
	"context"
	"fmt"
	"math"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/repository"
	"google.golang.org/protobuf/types/known/timestamppb"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
)

type VoucherService interface {
	CreateVoucher(ctx context.Context, req *proto_voucher_v1.CreateVoucherRequest) (*proto_voucher_v1.CreateVoucherResponse, error)
	GetVoucher(ctx context.Context, req *proto_voucher_v1.GetVoucherRequest) (*proto_voucher_v1.GetVoucherResponse, error)
	GetVoucherByCode(ctx context.Context, req *proto_voucher_v1.GetVoucherByCodeRequest) (*proto_voucher_v1.GetVoucherByCodeResponse, error)
	UpdateVoucher(ctx context.Context, req *proto_voucher_v1.UpdateVoucherRequest) (*proto_voucher_v1.UpdateVoucherResponse, error)
	ListVouchers(ctx context.Context, req *proto_voucher_v1.ListVouchersRequest) (*proto_voucher_v1.ListVouchersResponse, error)

	GetDiscountTypes(ctx context.Context, req *proto_voucher_v1.GetDiscountTypesRequest) (*proto_voucher_v1.GetDiscountTypesResponse, error)

	CheckEligibility(ctx context.Context, req *proto_voucher_v1.CheckVoucherEligibilityRequest) (*proto_voucher_v1.CheckVoucherEligibilityResponse, error)
	ListEligibleAutoVouchers(ctx context.Context, req *proto_voucher_v1.ListAutoEligibleVouchersRequest) (*proto_voucher_v1.ListAutoEligibleVouchersResponse, error)
}

type voucherService struct {
	repo        repository.VoucherRepository
	userClient  *clients.UserClient
	orderClient *clients.OrderClient
	logger      *logging.Logger
}

func NewVoucherService(repo repository.VoucherRepository, userClient *clients.UserClient, orderClient *clients.OrderClient, logger *logging.Logger) VoucherService {
	return &voucherService{repo: repo, userClient: userClient, orderClient: orderClient, logger: logger}
}

func (s *voucherService) CheckEligibility(ctx context.Context, req *proto_voucher_v1.CheckVoucherEligibilityRequest) (*proto_voucher_v1.CheckVoucherEligibilityResponse, error) {
	modelReq := &model.VoucherEligibilityRequest{
		VoucherCode:    req.VoucherCode,
		UserID:         req.UserId,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: req.OrderTimestamp.AsTime(),
		CartItems:      convertProtoCartItems(req.CartItems),
	}

	resp, err := s.repo.CheckVoucherEligibility(ctx, modelReq)
	if err != nil {
		return nil, errors.NewInternalError(fmt.Sprintf("failed to check voucher eligibility: %v", err))
	}

	voucherID := ""
	if resp.VoucherID != nil {
		voucherID = *resp.VoucherID
	}

	return &proto_voucher_v1.CheckVoucherEligibilityResponse{
		Eligible:       resp.Eligible,
		Message:        resp.Message,
		VoucherId:      voucherID,
		DiscountAmount: resp.DiscountAmount,
	}, nil
}

func (s *voucherService) ListEligibleAutoVouchers(ctx context.Context, req *proto_voucher_v1.ListAutoEligibleVouchersRequest) (*proto_voucher_v1.ListAutoEligibleVouchersResponse, error) {
	modelReq := &model.AutoVoucherEligibilityRequest{
		UserID:         req.UserId,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: req.OrderTimestamp.AsTime(),
		CartItems:      convertProtoCartItems(req.CartItems),
	}

	eligibleVouchers, err := s.repo.GetEligibleAutoVouchers(ctx, modelReq)
	if err != nil {
		return nil, errors.NewInternalError(fmt.Sprintf("failed to retrieve eligible vouchers: %v", err))
	}

	var responseVouchers []*proto_voucher_v1.EligibleVoucherInfo
	for _, voucher := range eligibleVouchers {
		responseVouchers = append(responseVouchers, &proto_voucher_v1.EligibleVoucherInfo{
			Eligible:       voucher.Eligible,
			Voucher:        convertVoucherToProtoVoucher(voucher.Voucher),
			DiscountAmount: voucher.DiscountAmount,
		})
	}

	return &proto_voucher_v1.ListAutoEligibleVouchersResponse{Vouchers: responseVouchers}, nil
}

func convertProtoCartItems(protoItems []*proto_voucher_v1.CartItem) []model.CartItem {
	var items []model.CartItem
	for _, item := range protoItems {
		modelItem := model.CartItem{
			Quantity: int(item.Quantity),
			Price:    item.Price,
		}
		if item.ProductId != nil {
			modelItem.ProductID = item.ProductId
		}
		if item.CategoryId != nil {
			modelItem.CategoryID = item.CategoryId
		}
		items = append(items, modelItem)
	}
	return items
}

func (s *voucherService) CreateVoucher(ctx context.Context, req *proto_voucher_v1.CreateVoucherRequest) (*proto_voucher_v1.CreateVoucherResponse, error) {
	modelReq := &model.CreateVoucherRequest{
		VoucherCode:    req.VoucherCode,
		Title:          req.Title,
		Description:    req.Description,
		DiscountTypeID: req.DiscountTypeId,
		DiscountValue:  req.DiscountValue,
		UsageMethod:    convertProtoUsageMethod(req.UsageMethod),
		ValidFrom:      req.ValidFrom.AsTime(),
		ValidUntil:     req.ValidUntil.AsTime(),
		MinOrderAmount: req.MinOrderAmount,
	}

	if req.MaxUsageCount != nil {
		count := int(*req.MaxUsageCount)
		modelReq.MaxUsageCount = &count
	}

	if req.MaxUsagePerUser != nil {
		count := int(*req.MaxUsagePerUser)
		modelReq.MaxUsagePerUser = &count
	}

	if req.MaxDiscountAmount != nil {
		modelReq.MaxDiscountAmount = req.MaxDiscountAmount
	}

	var createdBy string
	if userID, ok := auth.GetUserIDFromContext(ctx); ok && userID != "" {
		createdBy = userID
	} else if req.Metadata != nil && req.Metadata.UserId != "" {
		createdBy = req.Metadata.UserId
	}

	voucher, err := s.repo.Create(ctx, modelReq, createdBy)
	if err != nil {
		return nil, errors.NewInternalError(fmt.Sprintf("failed to create voucher: %v", err))
	}

	return &proto_voucher_v1.CreateVoucherResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) GetVoucher(ctx context.Context, req *proto_voucher_v1.GetVoucherRequest) (*proto_voucher_v1.GetVoucherResponse, error) {
	voucher, err := s.repo.GetByID(ctx, req.VoucherId)
	if err != nil {
		return nil, errors.NewNotFoundError(fmt.Sprintf("voucher with ID '%s' not found", req.VoucherId))
	}

	return &proto_voucher_v1.GetVoucherResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) GetVoucherByCode(ctx context.Context, req *proto_voucher_v1.GetVoucherByCodeRequest) (*proto_voucher_v1.GetVoucherByCodeResponse, error) {
	voucher, err := s.repo.GetByCode(ctx, req.VoucherCode)
	if err != nil {
		return nil, errors.NewNotFoundError(fmt.Sprintf("voucher with code '%s' not found", req.VoucherCode))
	}

	return &proto_voucher_v1.GetVoucherByCodeResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) UpdateVoucher(ctx context.Context, req *proto_voucher_v1.UpdateVoucherRequest) (*proto_voucher_v1.UpdateVoucherResponse, error) {
	modelReq := &model.UpdateVoucherRequest{
		Title:          req.Title,
		Description:    req.Description,
		DiscountTypeID: req.DiscountTypeId,
		DiscountValue:  req.DiscountValue,
		UsageMethod:    convertProtoUsageMethod(req.UsageMethod),
		Status:         convertProtoVoucherStatus(req.Status),
		MinOrderAmount: req.MinOrderAmount,
		ValidFrom:      req.ValidFrom.AsTime(),
		ValidUntil:     req.ValidUntil.AsTime(),
	}

	if req.MaxUsageCount != nil {
		count := int(*req.MaxUsageCount)
		modelReq.MaxUsageCount = &count
	}

	if req.MaxUsagePerUser != nil {
		count := int(*req.MaxUsagePerUser)
		modelReq.MaxUsagePerUser = &count
	}

	if req.MaxDiscountAmount != nil {
		modelReq.MaxDiscountAmount = req.MaxDiscountAmount
	}

	err := s.repo.Update(ctx, req.VoucherId, modelReq)
	if err != nil {
		return nil, errors.NewInternalError(fmt.Sprintf("failed to update voucher: %v", err))
	}

	voucher, err := s.repo.GetByID(ctx, req.VoucherId)
	if err != nil {
		return nil, errors.NewInternalError(fmt.Sprintf("failed to retrieve updated voucher: %v", err))
	}

	return &proto_voucher_v1.UpdateVoucherResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Voucher: convertVoucherToProtoVoucher(voucher),
	}, nil
}

func (s *voucherService) ListVouchers(ctx context.Context, req *proto_voucher_v1.ListVouchersRequest) (*proto_voucher_v1.ListVouchersResponse, error) {
	modelReq := &model.ListVouchersRequest{
		Page:      int(req.Page),
		Limit:     int(req.Limit),
		Search:    req.Search,
		Status:    req.Status,
		SortBy:    req.SortBy,
		SortOrder: req.SortOrder,
	}

	if req.DiscountTypeId != nil {
		modelReq.DiscountTypeID = req.DiscountTypeId
	}

	if req.UsageMethod != nil {
		usageMethod := convertProtoUsageMethod(*req.UsageMethod)
		modelReq.UsageMethod = &usageMethod
	}

	vouchers, total, err := s.repo.List(ctx, modelReq)
	if err != nil {
		return nil, errors.NewInternalError(fmt.Sprintf("failed to list vouchers: %v", err))
	}

	var protoVouchers []*proto_voucher_v1.Voucher
	for _, voucher := range vouchers {
		protoVouchers = append(protoVouchers, convertVoucherToProtoVoucher(voucher))
	}

	totalPages := int(math.Ceil(float64(total) / float64(modelReq.Limit)))

	return &proto_voucher_v1.ListVouchersResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		Vouchers:   protoVouchers,
		Total:      int32(total),
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: int32(totalPages),
	}, nil
}

func (s *voucherService) GetDiscountTypes(ctx context.Context, req *proto_voucher_v1.GetDiscountTypesRequest) (*proto_voucher_v1.GetDiscountTypesResponse, error) {
	discountTypes, err := s.repo.GetDiscountTypes(ctx)
	if err != nil {
		return nil, errors.NewInternalError(fmt.Sprintf("failed to get discount types: %v", err))
	}

	var protoDiscountTypes []*proto_voucher_v1.DiscountType
	for _, dt := range discountTypes {
		protoDiscountTypes = append(protoDiscountTypes, &proto_voucher_v1.DiscountType{
			Id:          dt.ID,
			TypeCode:    dt.TypeCode,
			TypeName:    dt.TypeName,
			Description: dt.Description,
			IsActive:    dt.IsActive,
			CreatedAt:   timestamppb.New(dt.CreatedAt),
			UpdatedAt:   timestamppb.New(dt.UpdatedAt),
		})
	}

	return &proto_voucher_v1.GetDiscountTypesResponse{
		Metadata: &proto_common_v1.ResponseMetadata{
			RequestId: req.Metadata.GetRequestId(),
		},
		DiscountTypes: protoDiscountTypes,
	}, nil
}

func convertProtoUsageMethod(protoMethod proto_voucher_v1.UsageMethod) model.UsageMethod {
	switch protoMethod {
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL:
		return model.UsageMethodManual
	case proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO:
		return model.UsageMethodAutomatic
	default:
		return model.UsageMethodManual
	}
}

func convertModelUsageMethod(modelMethod model.UsageMethod) proto_voucher_v1.UsageMethod {
	switch modelMethod {
	case model.UsageMethodManual:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	case model.UsageMethodAutomatic:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_AUTO
	default:
		return proto_voucher_v1.UsageMethod_USAGE_METHOD_MANUAL
	}
}

func convertProtoVoucherStatus(protoStatus proto_voucher_v1.VoucherStatus) string {
	switch protoStatus {
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE:
		return string(model.VoucherStatusActive)
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE:
		return string(model.VoucherStatusInactive)
	case proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED:
		return string(model.VoucherStatusExpired)
	default:
		return string(model.VoucherStatusActive)
	}
}

func convertVoucherToProtoVoucher(voucher *model.Voucher) *proto_voucher_v1.Voucher {
	if voucher == nil {
		return nil
	}

	protoVoucher := &proto_voucher_v1.Voucher{
		Id:                voucher.ID,
		VoucherCode:       voucher.VoucherCode,
		Title:             voucher.Title,
		Description:       voucher.Description,
		DiscountTypeId:    voucher.DiscountTypeID,
		DiscountValue:     voucher.DiscountValue,
		UsageMethod:       convertModelUsageMethod(voucher.UsageMethod),
		ValidFrom:         timestamppb.New(voucher.ValidFrom),
		ValidUntil:        timestamppb.New(voucher.ValidUntil),
		CurrentUsageCount: int32(voucher.CurrentUsageCount),
		MinOrderAmount:    voucher.MinOrderAmount,
		CreatedBy:         voucher.CreatedBy,
		CreatedAt:         timestamppb.New(voucher.CreatedAt),
		UpdatedAt:         timestamppb.New(voucher.UpdatedAt),
		TotalSavings:      voucher.TotalSavings,
		UniqueUsers:       int32(voucher.UniqueUsers),
	}

	if voucher.MaxUsageCount != nil {
		count := int32(*voucher.MaxUsageCount)
		protoVoucher.MaxUsageCount = &count
	}

	if voucher.MaxUsagePerUser != nil {
		count := int32(*voucher.MaxUsagePerUser)
		protoVoucher.MaxUsagePerUser = &count
	}

	if voucher.MaxDiscountAmount != nil {
		protoVoucher.MaxDiscountAmount = voucher.MaxDiscountAmount
	}

	switch voucher.Status {
	case model.VoucherStatusActive:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	case model.VoucherStatusInactive:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_INACTIVE
	case model.VoucherStatusExpired:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_EXPIRED
	default:
		protoVoucher.Status = proto_voucher_v1.VoucherStatus_VOUCHER_STATUS_ACTIVE
	}

	return protoVoucher
}
